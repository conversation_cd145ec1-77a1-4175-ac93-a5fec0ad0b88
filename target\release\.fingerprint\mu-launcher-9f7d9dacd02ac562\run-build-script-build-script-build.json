{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7767367476540183319, "build_script_build", false, 7636634688660076796], [14039947826026167952, "build_script_build", false, 4390194270912513681]], "local": [{"RerunIfChanged": {"output": "release\\build\\mu-launcher-9f7d9dacd02ac562\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}