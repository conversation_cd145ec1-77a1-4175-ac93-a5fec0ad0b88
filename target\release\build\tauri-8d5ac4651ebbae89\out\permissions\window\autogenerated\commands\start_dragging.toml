# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-start-dragging"
description = "Enables the start_dragging command without any pre-configured scope."
commands.allow = ["start_dragging"]

[[permission]]
identifier = "deny-start-dragging"
description = "Denies the start_dragging command without any pre-configured scope."
commands.deny = ["start_dragging"]
