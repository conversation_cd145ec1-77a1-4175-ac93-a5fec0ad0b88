import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Play, Square, Settings, Monitor, Volume2 } from 'lucide-react';
import { useApp } from '../../context/AppContext';

const LaunchPage: React.FC = () => {
  const { isGameRunning, setIsGameRunning } = useApp();
  const [gameSettings, setGameSettings] = useState({
    resolution: '1920x1080',
    fullscreen: true,
    volume: 80,
    quality: 'high'
  });

  const handleLaunchGame = async () => {
    setIsGameRunning(true);
    
    try {
      const result = await invoke('launch_game');
      console.log(result);
    } catch (error) {
      setIsGameRunning(false);
      console.error('启动游戏失败:', error);
    }
  };

  const handleStopGame = () => {
    setIsGameRunning(false);
    // 这里可以添加停止游戏的逻辑
  };

  return (
    <div className="p-8 space-y-8">
      {/* 游戏启动区域 */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">E-MU.ONLINE</h1>
          <p className="text-xl text-gray-400 mb-8">准备开始你的冒险之旅</p>
          
          <div className="flex justify-center space-x-4">
            {!isGameRunning ? (
              <button
                onClick={handleLaunchGame}
                className="launch-button px-12 py-4 rounded-lg font-bold text-xl flex items-center space-x-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                data-tauri-drag-region="false"
              >
                <Play size={28} />
                <span>启动游戏</span>
              </button>
            ) : (
              <button
                onClick={handleStopGame}
                className="px-12 py-4 rounded-lg font-bold text-xl flex items-center space-x-3 bg-red-600 hover:bg-red-700 transition-colors"
                data-tauri-drag-region="false"
              >
                <Square size={28} />
                <span>停止游戏</span>
              </button>
            )}
          </div>

          {isGameRunning && (
            <div className="mt-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
              <p className="text-green-400 font-semibold">游戏正在运行中...</p>
            </div>
          )}
        </div>
      </div>

      {/* 游戏设置 */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-2xl font-bold mb-6 flex items-center space-x-2">
          <Settings size={24} />
          <span>游戏设置</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 显示设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <Monitor size={20} />
              <span>显示设置</span>
            </h3>
            
            <div>
              <label className="block text-sm font-medium mb-2">分辨率</label>
              <select
                value={gameSettings.resolution}
                onChange={(e) => setGameSettings({...gameSettings, resolution: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="1920x1080">1920x1080</option>
                <option value="1680x1050">1680x1050</option>
                <option value="1440x900">1440x900</option>
                <option value="1366x768">1366x768</option>
                <option value="1280x720">1280x720</option>
              </select>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={gameSettings.fullscreen}
                  onChange={(e) => setGameSettings({...gameSettings, fullscreen: e.target.checked})}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span>全屏模式</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">画质设置</label>
              <select
                value={gameSettings.quality}
                onChange={(e) => setGameSettings({...gameSettings, quality: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="ultra">超高</option>
              </select>
            </div>
          </div>

          {/* 音频设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <Volume2 size={20} />
              <span>音频设置</span>
            </h3>
            
            <div>
              <label className="block text-sm font-medium mb-2">
                主音量: {gameSettings.volume}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={gameSettings.volume}
                onChange={(e) => setGameSettings({...gameSettings, volume: parseInt(e.target.value)})}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-700">
          <button
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors"
            data-tauri-drag-region="false"
          >
            保存设置
          </button>
        </div>
      </div>

      {/* 游戏信息 */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-2xl font-bold mb-4">游戏信息</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="bg-gray-700 rounded-lg p-4">
            <p className="text-2xl font-bold text-blue-400">v2.1.0</p>
            <p className="text-sm text-gray-400">当前版本</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <p className="text-2xl font-bold text-green-400">在线</p>
            <p className="text-sm text-gray-400">服务器状态</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <p className="text-2xl font-bold text-yellow-400">1,234</p>
            <p className="text-sm text-gray-400">在线玩家</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4">
            <p className="text-2xl font-bold text-purple-400">45ms</p>
            <p className="text-sm text-gray-400">延迟</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LaunchPage;
