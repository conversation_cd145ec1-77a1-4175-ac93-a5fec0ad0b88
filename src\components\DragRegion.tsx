import React, { useEffect, useRef } from 'react';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';

interface DragRegionProps {
  children: React.ReactNode;
  className?: string;
}

const DragRegion: React.FC<DragRegionProps> = ({ children, className = '' }) => {
  const dragRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const dragElement = dragRef.current;
    if (!dragElement) return;

    let isDragging = false;
    let startX = 0;
    let startY = 0;

    const handleMouseDown = async (e: MouseEvent) => {
      // 检查是否点击的是按钮或其他交互元素
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA') {
        return;
      }

      // 检查是否有 data-tauri-drag-region="false" 属性
      if (target.closest('[data-tauri-drag-region="false"]')) {
        return;
      }

      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;

      try {
        const window = getCurrentWebviewWindow();
        await window.startDragging();
      } catch (error) {
        console.error('Failed to start dragging:', error);
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      e.preventDefault();
    };

    const handleMouseUp = () => {
      isDragging = false;
    };

    dragElement.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      dragElement.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  return (
    <div ref={dragRef} className={className}>
      {children}
    </div>
  );
};

export default DragRegion;
