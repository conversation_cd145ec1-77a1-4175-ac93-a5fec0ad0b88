{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 9596618079566010223, "deps": [[2671782512663819132, "tauri_utils", false, 8336522234266761132], [3060637413840920116, "proc_macro2", false, 5096312196297537516], [4974441333307933176, "syn", false, 946335139158440780], [13077543566650298139, "heck", false, 1577398889775765422], [14455244907590647360, "tauri_codegen", false, 8934137927393975288], [17990358020177143287, "quote", false, 9304775612473768952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-19549f0db0de0301\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}