{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5456902659710135487, "path": 11018751307580229710, "deps": [[9689903380558560274, "serde", false, 9841288889961963921], [11690957875220028834, "serde_with_macros", false, 14208078334975563244], [16257276029081467297, "serde_derive", false, 5576570851292141867]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\serde_with-e498ac078ab3825c\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}