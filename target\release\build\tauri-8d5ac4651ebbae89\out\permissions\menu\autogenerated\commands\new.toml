# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-new"
description = "Enables the new command without any pre-configured scope."
commands.allow = ["new"]

[[permission]]
identifier = "deny-new"
description = "Denies the new command without any pre-configured scope."
commands.deny = ["new"]
