{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6620145502275485423, "deps": [[376837177317575824, "softbuffer", false, 666688205791498378], [2013030631243296465, "webview2_com", false, 11257737392710050888], [2671782512663819132, "tauri_utils", false, 7047514176211349907], [3150220818285335163, "url", false, 7301881132893300184], [3722963349756955755, "once_cell", false, 14521976164538839542], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5986029879202738730, "log", false, 5047229584628730050], [6089812615193535349, "tauri_runtime", false, 12194256462537513187], [8826339825490770380, "tao", false, 4267502409829733113], [9010263965687315507, "http", false, 15629939712500996649], [9141053277961803901, "wry", false, 16765033041532806421], [11599800339996261026, "build_script_build", false, 16011662610975593677], [14585479307175734061, "windows", false, 13749503539899060250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-cefe3fb6d770994b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}