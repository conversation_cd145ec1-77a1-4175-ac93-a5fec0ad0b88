{"rustc": 1842507548689473721, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 3402796170130056749, "deps": [[2671782512663819132, "tauri_utils", false, 7047514176211349907], [3150220818285335163, "url", false, 7301881132893300184], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [6089812615193535349, "build_script_build", false, 17355528663036265493], [7606335748176206944, "dpi", false, 10125233705909480676], [9010263965687315507, "http", false, 15629939712500996649], [9689903380558560274, "serde", false, 14326913226427773042], [10806645703491011684, "thiserror", false, 6923350312884292379], [14585479307175734061, "windows", false, 13749503539899060250], [15367738274754116744, "serde_json", false, 6815494230538151593], [16727543399706004146, "cookie", false, 13002483928728026112]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-9fef82ed193b4a12\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}