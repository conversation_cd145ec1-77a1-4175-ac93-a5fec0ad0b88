@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-900 text-white;
  }
}

/* Tauri 拖拽区域样式 */
[data-tauri-drag-region] {
  -webkit-app-region: drag;
  app-region: drag;
}

[data-tauri-drag-region="false"] {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* 确保按钮和交互元素不被拖拽 */
button, input, select, textarea, a {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* 确保滚动区域不被拖拽影响 */
.overflow-y-auto, .overflow-auto, .overflow-scroll {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* 确保内容区域可以正常滚动 */
[data-scrollable="true"] {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 游戏启动按钮特效 */
.launch-button {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.launch-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.launch-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.launch-button:hover::before {
  left: 100%;
}
