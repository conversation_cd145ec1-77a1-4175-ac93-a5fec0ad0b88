{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 16847186827226506596, "profile": 2040997289075261528, "path": 10763286916239946207, "deps": [[7767367476540183319, "build_script_build", false, 13068344560635911059], [8218178811151724123, "reqwest", false, 11573679000559427266], [9538054652646069845, "tokio", false, 5970496291290527155], [9689903380558560274, "serde", false, 9841288889961963921], [13625485746686963219, "anyhow", false, 2431689092402825966], [14039947826026167952, "tauri", false, 6277002405556329837], [15367738274754116744, "serde_json", false, 13225267877164477003]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\mu-launcher-280b7dba6ce8d782\\dep-lib-mu_launcher", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}