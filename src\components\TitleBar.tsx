import React from 'react';
import { Minus, Square, X } from 'lucide-react';
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow';

const TitleBar: React.FC = () => {
  const handleMinimize = async () => {
    const window = getCurrentWebviewWindow();
    await window.minimize();
  };

  const handleMaximize = async () => {
    const window = getCurrentWebviewWindow();
    await window.toggleMaximize();
  };

  const handleClose = async () => {
    const window = getCurrentWebviewWindow();
    await window.close();
  };

  return (
    <div
      className="flex items-center justify-between h-10 bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-sm border-b border-gray-700/30 select-none shadow-lg"
      data-tauri-drag-region
    >
      {/* 左侧 - 应用标题 */}
      <div className="flex items-center px-4">
        <div className="w-5 h-5 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-md mr-3 shadow-sm"></div>
        <span className="text-sm font-semibold text-gray-100 tracking-wide">E-MU.ONLINE Launcher</span>
      </div>

      {/* 右侧 - 窗口控制按钮 */}
      <div className="flex">
        <button
          onClick={handleMinimize}
          className="w-12 h-10 flex items-center justify-center hover:bg-gray-700/60 transition-all duration-200 hover:shadow-inner"
          title="最小化"
          data-tauri-drag-region="false"
        >
          <Minus size={16} className="text-gray-300 hover:text-white transition-colors" />
        </button>
        <button
          onClick={handleMaximize}
          className="w-12 h-10 flex items-center justify-center hover:bg-gray-700/60 transition-all duration-200 hover:shadow-inner"
          title="最大化"
          data-tauri-drag-region="false"
        >
          <Square size={14} className="text-gray-300 hover:text-white transition-colors" />
        </button>
        <button
          onClick={handleClose}
          className="w-12 h-10 flex items-center justify-center hover:bg-red-600 transition-all duration-200 group hover:shadow-inner"
          title="关闭"
          data-tauri-drag-region="false"
        >
          <X size={16} className="text-gray-300 group-hover:text-white transition-colors" />
        </button>
      </div>
    </div>
  );
};

export default TitleBar;
