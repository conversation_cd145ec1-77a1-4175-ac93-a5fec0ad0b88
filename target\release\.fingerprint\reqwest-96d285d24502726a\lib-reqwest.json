{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 304012281195555478, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [418947936956741439, "h2", false, 5942827075006888075], [778154619793643451, "hyper_util", false, 10624252645661836338], [784494742817713399, "tower_service", false, 5062019019135106002], [1906322745568073236, "pin_project_lite", false, 2389057907062384012], [2054153378684941554, "tower_http", false, 3750909507697906882], [2517136641825875337, "sync_wrapper", false, 8905282965693471066], [2883436298747778685, "rustls_pki_types", false, 18021711389995360], [3150220818285335163, "url", false, 14565937691566033650], [5695049318159433696, "tower", false, 16749678324006968994], [5986029879202738730, "log", false, 7783758905256315459], [7620660491849607393, "futures_core", false, 4359839236934194262], [9010263965687315507, "http", false, 1862789691165185595], [9538054652646069845, "tokio", false, 5970496291290527155], [9689903380558560274, "serde", false, 9841288889961963921], [10229185211513642314, "mime", false, 9651018063964816897], [11957360342995674422, "hyper", false, 7001017453720284047], [12186126227181294540, "tokio_native_tls", false, 3005589013549822813], [13077212702700853852, "base64", false, 16878550808635762142], [14084095096285906100, "http_body", false, 15356474674297432158], [14564311161534545801, "encoding_rs", false, 1044314460687697280], [15367738274754116744, "serde_json", false, 13225267877164477003], [16066129441945555748, "bytes", false, 5746740623261701503], [16542808166767769916, "serde_urlencoded", false, 1183088342104923807], [16785601910559813697, "native_tls_crate", false, 37383817686084257], [16900715236047033623, "http_body_util", false, 7220491532981291408], [18273243456331255970, "hyper_tls", false, 5315821237360951234]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-96d285d24502726a\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}