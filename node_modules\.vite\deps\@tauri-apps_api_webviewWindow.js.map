{"version": 3, "sources": ["../../@tauri-apps/api/dpi.js", "../../@tauri-apps/api/event.js", "../../@tauri-apps/api/image.js", "../../@tauri-apps/api/window.js", "../../@tauri-apps/api/webview.js", "../../@tauri-apps/api/webviewWindow.js"], "sourcesContent": ["import { SERIALIZE_TO_IPC_FN } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * A size represented in logical pixels.\n * Logical pixels are scaled according to the window's DPI scale.\n * Most browser APIs (i.e. `MouseEvent`'s `clientX`) will return logical pixels.\n *\n * For logical-pixel-based position, see {@linkcode LogicalPosition}.\n *\n * @since 2.0.0\n */\nclass LogicalSize {\n    constructor(...args) {\n        this.type = 'Logical';\n        if (args.length === 1) {\n            if ('Logical' in args[0]) {\n                this.width = args[0].Logical.width;\n                this.height = args[0].Logical.height;\n            }\n            else {\n                this.width = args[0].width;\n                this.height = args[0].height;\n            }\n        }\n        else {\n            this.width = args[0];\n            this.height = args[1];\n        }\n    }\n    /**\n     * Converts the logical size to a physical one.\n     * @example\n     * ```typescript\n     * import { LogicalSize } from '@tauri-apps/api/dpi';\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     *\n     * const appWindow = getCurrentWindow();\n     * const factor = await appWindow.scaleFactor();\n     * const size = new LogicalSize(400, 500);\n     * const physical = size.toPhysical(factor);\n     * ```\n     *\n     * @since 2.0.0\n     */\n    toPhysical(scaleFactor) {\n        return new PhysicalSize(this.width * scaleFactor, this.height * scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            width: this.width,\n            height: this.height\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n/**\n * A size represented in physical pixels.\n *\n * Physical pixels represent actual screen pixels, and are DPI-independent.\n * For high-DPI windows, this means that any point in the window on the screen\n * will have a different position in logical pixels (@linkcode LogicalSize).\n *\n * For physical-pixel-based position, see {@linkcode PhysicalPosition}.\n *\n * @since 2.0.0\n */\nclass PhysicalSize {\n    constructor(...args) {\n        this.type = 'Physical';\n        if (args.length === 1) {\n            if ('Physical' in args[0]) {\n                this.width = args[0].Physical.width;\n                this.height = args[0].Physical.height;\n            }\n            else {\n                this.width = args[0].width;\n                this.height = args[0].height;\n            }\n        }\n        else {\n            this.width = args[0];\n            this.height = args[1];\n        }\n    }\n    /**\n     * Converts the physical size to a logical one.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const appWindow = getCurrentWindow();\n     * const factor = await appWindow.scaleFactor();\n     * const size = await appWindow.innerSize(); // PhysicalSize\n     * const logical = size.toLogical(factor);\n     * ```\n     */\n    toLogical(scaleFactor) {\n        return new LogicalSize(this.width / scaleFactor, this.height / scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            width: this.width,\n            height: this.height\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n/**\n * A size represented either in physical or in logical pixels.\n *\n * This type is basically a union type of {@linkcode LogicalSize} and {@linkcode PhysicalSize}\n * but comes in handy when using `tauri::Size` in Rust as an argument to a command, as this class\n * automatically serializes into a valid format so it can be deserialized correctly into `tauri::Size`\n *\n * So instead of\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * import { LogicalSize, PhysicalSize } from '@tauri-apps/api/dpi';\n *\n * const size: LogicalSize | PhysicalSize = someFunction(); // where someFunction returns either LogicalSize or PhysicalSize\n * const validSize = size instanceof LogicalSize\n *   ? { Logical: { width: size.width, height: size.height } }\n *   : { Physical: { width: size.width, height: size.height } }\n * await invoke(\"do_something_with_size\", { size: validSize });\n * ```\n *\n * You can just use {@linkcode Size}\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * import { LogicalSize, PhysicalSize, Size } from '@tauri-apps/api/dpi';\n *\n * const size: LogicalSize | PhysicalSize = someFunction(); // where someFunction returns either LogicalSize or PhysicalSize\n * const validSize = new Size(size);\n * await invoke(\"do_something_with_size\", { size: validSize });\n * ```\n *\n * @since 2.1.0\n */\nclass Size {\n    constructor(size) {\n        this.size = size;\n    }\n    toLogical(scaleFactor) {\n        return this.size instanceof LogicalSize\n            ? this.size\n            : this.size.toLogical(scaleFactor);\n    }\n    toPhysical(scaleFactor) {\n        return this.size instanceof PhysicalSize\n            ? this.size\n            : this.size.toPhysical(scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            [`${this.size.type}`]: {\n                width: this.size.width,\n                height: this.size.height\n            }\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n/**\n *  A position represented in logical pixels.\n * For an explanation of what logical pixels are, see description of {@linkcode LogicalSize}.\n *\n * @since 2.0.0\n */\nclass LogicalPosition {\n    constructor(...args) {\n        this.type = 'Logical';\n        if (args.length === 1) {\n            if ('Logical' in args[0]) {\n                this.x = args[0].Logical.x;\n                this.y = args[0].Logical.y;\n            }\n            else {\n                this.x = args[0].x;\n                this.y = args[0].y;\n            }\n        }\n        else {\n            this.x = args[0];\n            this.y = args[1];\n        }\n    }\n    /**\n     * Converts the logical position to a physical one.\n     * @example\n     * ```typescript\n     * import { LogicalPosition } from '@tauri-apps/api/dpi';\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     *\n     * const appWindow = getCurrentWindow();\n     * const factor = await appWindow.scaleFactor();\n     * const position = new LogicalPosition(400, 500);\n     * const physical = position.toPhysical(factor);\n     * ```\n     *\n     * @since 2.0.0\n     */\n    toPhysical(scaleFactor) {\n        return new PhysicalPosition(this.x * scaleFactor, this.y * scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            x: this.x,\n            y: this.y\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n/**\n *  A position represented in physical pixels.\n *\n * For an explanation of what physical pixels are, see description of {@linkcode PhysicalSize}.\n *\n * @since 2.0.0\n */\nclass PhysicalPosition {\n    constructor(...args) {\n        this.type = 'Physical';\n        if (args.length === 1) {\n            if ('Physical' in args[0]) {\n                this.x = args[0].Physical.x;\n                this.y = args[0].Physical.y;\n            }\n            else {\n                this.x = args[0].x;\n                this.y = args[0].y;\n            }\n        }\n        else {\n            this.x = args[0];\n            this.y = args[1];\n        }\n    }\n    /**\n     * Converts the physical position to a logical one.\n     * @example\n     * ```typescript\n     * import { PhysicalPosition } from '@tauri-apps/api/dpi';\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     *\n     * const appWindow = getCurrentWindow();\n     * const factor = await appWindow.scaleFactor();\n     * const position = new PhysicalPosition(400, 500);\n     * const physical = position.toLogical(factor);\n     * ```\n     *\n     * @since 2.0.0\n     */\n    toLogical(scaleFactor) {\n        return new LogicalPosition(this.x / scaleFactor, this.y / scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            x: this.x,\n            y: this.y\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n/**\n * A position represented either in physical or in logical pixels.\n *\n * This type is basically a union type of {@linkcode LogicalSize} and {@linkcode PhysicalSize}\n * but comes in handy when using `tauri::Position` in Rust as an argument to a command, as this class\n * automatically serializes into a valid format so it can be deserialized correctly into `tauri::Position`\n *\n * So instead of\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * import { LogicalPosition, PhysicalPosition } from '@tauri-apps/api/dpi';\n *\n * const position: LogicalPosition | PhysicalPosition = someFunction(); // where someFunction returns either LogicalPosition or PhysicalPosition\n * const validPosition = position instanceof LogicalPosition\n *   ? { Logical: { x: position.x, y: position.y } }\n *   : { Physical: { x: position.x, y: position.y } }\n * await invoke(\"do_something_with_position\", { position: validPosition });\n * ```\n *\n * You can just use {@linkcode Position}\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * import { LogicalPosition, PhysicalPosition, Position } from '@tauri-apps/api/dpi';\n *\n * const position: LogicalPosition | PhysicalPosition = someFunction(); // where someFunction returns either LogicalPosition or PhysicalPosition\n * const validPosition = new Position(position);\n * await invoke(\"do_something_with_position\", { position: validPosition });\n * ```\n *\n * @since 2.1.0\n */\nclass Position {\n    constructor(position) {\n        this.position = position;\n    }\n    toLogical(scaleFactor) {\n        return this.position instanceof LogicalPosition\n            ? this.position\n            : this.position.toLogical(scaleFactor);\n    }\n    toPhysical(scaleFactor) {\n        return this.position instanceof PhysicalPosition\n            ? this.position\n            : this.position.toPhysical(scaleFactor);\n    }\n    [SERIALIZE_TO_IPC_FN]() {\n        return {\n            [`${this.position.type}`]: {\n                x: this.position.x,\n                y: this.position.y\n            }\n        };\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\n\nexport { LogicalPosition, LogicalSize, PhysicalPosition, PhysicalSize, Position, Size };\n", "import { invoke, transformCallback } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The event system allows you to emit events to the backend and listen to events from it.\n *\n * This package is also accessible with `window.__TAURI__.event` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * @since 1.1.0\n */\nvar TauriEvent;\n(function (TauriEvent) {\n    TauriEvent[\"WINDOW_RESIZED\"] = \"tauri://resize\";\n    TauriEvent[\"WINDOW_MOVED\"] = \"tauri://move\";\n    TauriEvent[\"WINDOW_CLOSE_REQUESTED\"] = \"tauri://close-requested\";\n    TauriEvent[\"WINDOW_DESTROYED\"] = \"tauri://destroyed\";\n    TauriEvent[\"WINDOW_FOCUS\"] = \"tauri://focus\";\n    TauriEvent[\"WINDOW_BLUR\"] = \"tauri://blur\";\n    TauriEvent[\"WINDOW_SCALE_FACTOR_CHANGED\"] = \"tauri://scale-change\";\n    TauriEvent[\"WINDOW_THEME_CHANGED\"] = \"tauri://theme-changed\";\n    TauriEvent[\"WINDOW_CREATED\"] = \"tauri://window-created\";\n    TauriEvent[\"WEBVIEW_CREATED\"] = \"tauri://webview-created\";\n    TauriEvent[\"DRAG_ENTER\"] = \"tauri://drag-enter\";\n    TauriEvent[\"DRAG_OVER\"] = \"tauri://drag-over\";\n    TauriEvent[\"DRAG_DROP\"] = \"tauri://drag-drop\";\n    TauriEvent[\"DRAG_LEAVE\"] = \"tauri://drag-leave\";\n})(TauriEvent || (TauriEvent = {}));\n/**\n * Unregister the event listener associated with the given name and id.\n *\n * @ignore\n * @param event The event name\n * @param eventId Event identifier\n * @returns\n */\nasync function _unlisten(event, eventId) {\n    window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener(event, eventId);\n    await invoke('plugin:event|unlisten', {\n        event,\n        eventId\n    });\n}\n/**\n * Listen to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { listen } from '@tauri-apps/api/event';\n * const unlisten = await listen<string>('error', (event) => {\n *   console.log(`Got error, payload: ${event.payload}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function listen(event, handler, options) {\n    var _a;\n    const target = typeof (options === null || options === void 0 ? void 0 : options.target) === 'string'\n        ? { kind: 'AnyLabel', label: options.target }\n        : ((_a = options === null || options === void 0 ? void 0 : options.target) !== null && _a !== void 0 ? _a : { kind: 'Any' });\n    return invoke('plugin:event|listen', {\n        event,\n        target,\n        handler: transformCallback(handler)\n    }).then((eventId) => {\n        return async () => _unlisten(event, eventId);\n    });\n}\n/**\n * Listens once to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { once } from '@tauri-apps/api/event';\n * interface LoadedPayload {\n *   loggedIn: boolean,\n *   token: string\n * }\n * const unlisten = await once<LoadedPayload>('loaded', (event) => {\n *   console.log(`App is loaded, loggedIn: ${event.payload.loggedIn}, token: ${event.payload.token}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function once(event, handler, options) {\n    return listen(event, (eventData) => {\n        void _unlisten(event, eventData.id);\n        handler(eventData);\n    }, options);\n}\n/**\n * Emits an event to all {@link EventTarget|targets}.\n *\n * @example\n * ```typescript\n * import { emit } from '@tauri-apps/api/event';\n * await emit('frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 1.0.0\n */\nasync function emit(event, payload) {\n    await invoke('plugin:event|emit', {\n        event,\n        payload\n    });\n}\n/**\n * Emits an event to all {@link EventTarget|targets} matching the given target.\n *\n * @example\n * ```typescript\n * import { emitTo } from '@tauri-apps/api/event';\n * await emitTo('main', 'frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 2.0.0\n */\nasync function emitTo(target, event, payload) {\n    const eventTarget = typeof target === 'string' ? { kind: 'AnyLabel', label: target } : target;\n    await invoke('plugin:event|emit_to', {\n        target: eventTarget,\n        event,\n        payload\n    });\n}\n\nexport { TauriEvent, emit, emitTo, listen, once };\n", "import { Resource, invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** An RGBA Image in row-major order from top to bottom. */\nclass Image extends Resource {\n    /**\n     * Creates an Image from a resource ID. For internal use only.\n     *\n     * @ignore\n     */\n    constructor(rid) {\n        super(rid);\n    }\n    /** Creates a new Image using RGBA data, in row-major order from top to bottom, and with specified width and height. */\n    static async new(rgba, width, height) {\n        return invoke('plugin:image|new', {\n            rgba: transformImage(rgba),\n            width,\n            height\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided bytes by inferring the file format.\n     * If the format is known, prefer [@link Image.fromPngBytes] or [@link Image.fromIcoBytes].\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromBytes(bytes) {\n        return invoke('plugin:image|from_bytes', {\n            bytes: transformImage(bytes)\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided path.\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromPath(path) {\n        return invoke('plugin:image|from_path', { path }).then((rid) => new Image(rid));\n    }\n    /** Returns the RGBA data for this image, in row-major order from top to bottom.  */\n    async rgba() {\n        return invoke('plugin:image|rgba', {\n            rid: this.rid\n        }).then((buffer) => new Uint8Array(buffer));\n    }\n    /** Returns the size of this image.  */\n    async size() {\n        return invoke('plugin:image|size', { rid: this.rid });\n    }\n}\n/**\n * Transforms image from various types into a type acceptable by Rust.\n *\n * See [tauri::image::JsImage](https://docs.rs/tauri/2/tauri/image/enum.JsImage.html) for more information.\n * Note the API signature is not stable and might change.\n */\nfunction transformImage(image) {\n    const ret = image == null\n        ? null\n        : typeof image === 'string'\n            ? image\n            : image instanceof Image\n                ? image.rid\n                : image;\n    return ret;\n}\n\nexport { Image, transformImage };\n", "import { PhysicalPosition, PhysicalSize, Size, Position } from './dpi.js';\nexport { LogicalPosition, LogicalSize } from './dpi.js';\nimport { listen, once, emit, emitTo, TauriEvent } from './event.js';\nimport { invoke } from './core.js';\nimport { transformImage } from './image.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides APIs to create windows, communicate with other windows and manipulate the current window.\n *\n * #### Window events\n *\n * Events can be listened to using {@link Window.listen}:\n * ```typescript\n * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n * getCurrentWindow().listen(\"my-window-event\", ({ event, payload }) => { });\n * ```\n *\n * @module\n */\n/**\n * Attention type to request on a window.\n *\n * @since 1.0.0\n */\nvar UserAttentionType;\n(function (UserAttentionType) {\n    /**\n     * #### Platform-specific\n     * - **macOS:** Bounces the dock icon until the application is in focus.\n     * - **Windows:** Flashes both the window and the taskbar button until the application is in focus.\n     */\n    UserAttentionType[UserAttentionType[\"Critical\"] = 1] = \"Critical\";\n    /**\n     * #### Platform-specific\n     * - **macOS:** Bounces the dock icon once.\n     * - **Windows:** Flashes the taskbar button until the application is in focus.\n     */\n    UserAttentionType[UserAttentionType[\"Informational\"] = 2] = \"Informational\";\n})(UserAttentionType || (UserAttentionType = {}));\nclass CloseRequestedEvent {\n    constructor(event) {\n        this._preventDefault = false;\n        this.event = event.event;\n        this.id = event.id;\n    }\n    preventDefault() {\n        this._preventDefault = true;\n    }\n    isPreventDefault() {\n        return this._preventDefault;\n    }\n}\nvar ProgressBarStatus;\n(function (ProgressBarStatus) {\n    /**\n     * Hide progress bar.\n     */\n    ProgressBarStatus[\"None\"] = \"none\";\n    /**\n     * Normal state.\n     */\n    ProgressBarStatus[\"Normal\"] = \"normal\";\n    /**\n     * Indeterminate state. **Treated as Normal on Linux and macOS**\n     */\n    ProgressBarStatus[\"Indeterminate\"] = \"indeterminate\";\n    /**\n     * Paused state. **Treated as Normal on Linux**\n     */\n    ProgressBarStatus[\"Paused\"] = \"paused\";\n    /**\n     * Error state. **Treated as Normal on linux**\n     */\n    ProgressBarStatus[\"Error\"] = \"error\";\n})(ProgressBarStatus || (ProgressBarStatus = {}));\n/**\n * Get an instance of `Window` for the current window.\n *\n * @since 1.0.0\n */\nfunction getCurrentWindow() {\n    return new Window(window.__TAURI_INTERNALS__.metadata.currentWindow.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\n/**\n * Gets a list of instances of `Window` for all available windows.\n *\n * @since 1.0.0\n */\nasync function getAllWindows() {\n    return invoke('plugin:window|get_all_windows').then((windows) => windows.map((w) => new Window(w, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    })));\n}\n/** @ignore */\n// events that are emitted right here instead of by the created window\nconst localTauriEvents = ['tauri://created', 'tauri://error'];\n/**\n * Create new window or get a handle to an existing one.\n *\n * Windows are identified by a *label*  a unique identifier that can be used to reference it later.\n * It may only contain alphanumeric characters `a-zA-Z` plus the following special characters `-`, `/`, `:` and `_`.\n *\n * @example\n * ```typescript\n * import { Window } from \"@tauri-apps/api/window\"\n *\n * const appWindow = new Window('theUniqueLabel');\n *\n * appWindow.once('tauri://created', function () {\n *  // window successfully created\n * });\n * appWindow.once('tauri://error', function (e) {\n *  // an error happened creating the window\n * });\n *\n * // emit an event to the backend\n * await appWindow.emit(\"some-event\", \"data\");\n * // listen to an event from the backend\n * const unlisten = await appWindow.listen(\"event-name\", e => {});\n * unlisten();\n * ```\n *\n * @since 2.0.0\n */\nclass Window {\n    /**\n     * Creates a new Window.\n     * @example\n     * ```typescript\n     * import { Window } from '@tauri-apps/api/window';\n     * const appWindow = new Window('my-label');\n     * appWindow.once('tauri://created', function () {\n     *  // window successfully created\n     * });\n     * appWindow.once('tauri://error', function (e) {\n     *  // an error happened creating the window\n     * });\n     * ```\n     *\n     * @param label The unique window label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The {@link Window} instance to communicate with the window.\n     */\n    constructor(label, options = {}) {\n        var _a;\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WindowOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invoke('plugin:window|create', {\n                options: {\n                    ...options,\n                    parent: typeof options.parent === 'string'\n                        ? options.parent\n                        : (_a = options.parent) === null || _a === void 0 ? void 0 : _a.label,\n                    label\n                }\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the Window associated with the given label.\n     * @example\n     * ```typescript\n     * import { Window } from '@tauri-apps/api/window';\n     * const mainWindow = Window.getByLabel('main');\n     * ```\n     *\n     * @param label The window label.\n     * @returns The Window instance to communicate with the window or null if the window doesn't exist.\n     */\n    static async getByLabel(label) {\n        var _a;\n        return (_a = (await getAllWindows()).find((w) => w.label === label)) !== null && _a !== void 0 ? _a : null;\n    }\n    /**\n     * Get an instance of `Window` for the current window.\n     */\n    static getCurrent() {\n        return getCurrentWindow();\n    }\n    /**\n     * Gets a list of instances of `Window` for all available windows.\n     */\n    static async getAll() {\n        return getAllWindows();\n    }\n    /**\n     *  Gets the focused window.\n     * @example\n     * ```typescript\n     * import { Window } from '@tauri-apps/api/window';\n     * const focusedWindow = Window.getFocusedWindow();\n     * ```\n     *\n     * @returns The Window instance or `undefined` if there is not any focused window.\n     */\n    static async getFocusedWindow() {\n        for (const w of await getAllWindows()) {\n            if (await w.isFocused()) {\n                return w;\n            }\n        }\n        return null;\n    }\n    /**\n     * Listen to an emitted event on this window.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const unlisten = await getCurrentWindow().listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return listen(event, handler, {\n            target: { kind: 'Window', label: this.label }\n        });\n    }\n    /**\n     * Listen to an emitted event on this window only once.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const unlisten = await getCurrentWindow().once<null>('initialized', (event) => {\n     *   console.log(`Window initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return once(event, handler, {\n            target: { kind: 'Window', label: this.label }\n        });\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets}.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().emit('window-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emit(event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emit(event, payload);\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets} matching the given target.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().emit('main', 'window-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emitTo(target, event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line security/detect-object-injection\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emitTo(target, event, payload);\n    }\n    /** @ignore */\n    _handleTauriEvent(event, handler) {\n        if (localTauriEvents.includes(event)) {\n            if (!(event in this.listeners)) {\n                // eslint-disable-next-line\n                this.listeners[event] = [handler];\n            }\n            else {\n                // eslint-disable-next-line\n                this.listeners[event].push(handler);\n            }\n            return true;\n        }\n        return false;\n    }\n    // Getters\n    /**\n     * The scale factor that can be used to map physical pixels to logical pixels.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const factor = await getCurrentWindow().scaleFactor();\n     * ```\n     *\n     * @returns The window's monitor scale factor.\n     */\n    async scaleFactor() {\n        return invoke('plugin:window|scale_factor', {\n            label: this.label\n        });\n    }\n    /**\n     * The position of the top-left hand corner of the window's client area relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const position = await getCurrentWindow().innerPosition();\n     * ```\n     *\n     * @returns The window's inner position.\n     */\n    async innerPosition() {\n        return invoke('plugin:window|inner_position', {\n            label: this.label\n        }).then((p) => new PhysicalPosition(p));\n    }\n    /**\n     * The position of the top-left hand corner of the window relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const position = await getCurrentWindow().outerPosition();\n     * ```\n     *\n     * @returns The window's outer position.\n     */\n    async outerPosition() {\n        return invoke('plugin:window|outer_position', {\n            label: this.label\n        }).then((p) => new PhysicalPosition(p));\n    }\n    /**\n     * The physical size of the window's client area.\n     * The client area is the content of the window, excluding the title bar and borders.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const size = await getCurrentWindow().innerSize();\n     * ```\n     *\n     * @returns The window's inner size.\n     */\n    async innerSize() {\n        return invoke('plugin:window|inner_size', {\n            label: this.label\n        }).then((s) => new PhysicalSize(s));\n    }\n    /**\n     * The physical size of the entire window.\n     * These dimensions include the title bar and borders. If you don't want that (and you usually don't), use inner_size instead.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const size = await getCurrentWindow().outerSize();\n     * ```\n     *\n     * @returns The window's outer size.\n     */\n    async outerSize() {\n        return invoke('plugin:window|outer_size', {\n            label: this.label\n        }).then((s) => new PhysicalSize(s));\n    }\n    /**\n     * Gets the window's current fullscreen state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const fullscreen = await getCurrentWindow().isFullscreen();\n     * ```\n     *\n     * @returns Whether the window is in fullscreen mode or not.\n     */\n    async isFullscreen() {\n        return invoke('plugin:window|is_fullscreen', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current minimized state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const minimized = await getCurrentWindow().isMinimized();\n     * ```\n     */\n    async isMinimized() {\n        return invoke('plugin:window|is_minimized', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current maximized state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const maximized = await getCurrentWindow().isMaximized();\n     * ```\n     *\n     * @returns Whether the window is maximized or not.\n     */\n    async isMaximized() {\n        return invoke('plugin:window|is_maximized', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current focus state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const focused = await getCurrentWindow().isFocused();\n     * ```\n     *\n     * @returns Whether the window is focused or not.\n     */\n    async isFocused() {\n        return invoke('plugin:window|is_focused', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current decorated state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const decorated = await getCurrentWindow().isDecorated();\n     * ```\n     *\n     * @returns Whether the window is decorated or not.\n     */\n    async isDecorated() {\n        return invoke('plugin:window|is_decorated', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current resizable state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const resizable = await getCurrentWindow().isResizable();\n     * ```\n     *\n     * @returns Whether the window is resizable or not.\n     */\n    async isResizable() {\n        return invoke('plugin:window|is_resizable', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's native maximize button state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const maximizable = await getCurrentWindow().isMaximizable();\n     * ```\n     *\n     * @returns Whether the window's native maximize button is enabled or not.\n     */\n    async isMaximizable() {\n        return invoke('plugin:window|is_maximizable', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's native minimize button state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const minimizable = await getCurrentWindow().isMinimizable();\n     * ```\n     *\n     * @returns Whether the window's native minimize button is enabled or not.\n     */\n    async isMinimizable() {\n        return invoke('plugin:window|is_minimizable', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's native close button state.\n     *\n     * #### Platform-specific\n     *\n     * - **iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const closable = await getCurrentWindow().isClosable();\n     * ```\n     *\n     * @returns Whether the window's native close button is enabled or not.\n     */\n    async isClosable() {\n        return invoke('plugin:window|is_closable', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current visible state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const visible = await getCurrentWindow().isVisible();\n     * ```\n     *\n     * @returns Whether the window is visible or not.\n     */\n    async isVisible() {\n        return invoke('plugin:window|is_visible', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current title.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const title = await getCurrentWindow().title();\n     * ```\n     */\n    async title() {\n        return invoke('plugin:window|title', {\n            label: this.label\n        });\n    }\n    /**\n     * Gets the window's current theme.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Theme was introduced on macOS 10.14. Returns `light` on macOS 10.13 and below.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const theme = await getCurrentWindow().theme();\n     * ```\n     *\n     * @returns The window theme.\n     */\n    async theme() {\n        return invoke('plugin:window|theme', {\n            label: this.label\n        });\n    }\n    /**\n     * Whether the window is configured to be always on top of other windows or not.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * const alwaysOnTop = await getCurrentWindow().isAlwaysOnTop();\n     * ```\n     *\n     * @returns Whether the window is visible or not.\n     */\n    async isAlwaysOnTop() {\n        return invoke('plugin:window|is_always_on_top', {\n            label: this.label\n        });\n    }\n    // Setters\n    /**\n     * Centers the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().center();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async center() {\n        return invoke('plugin:window|center', {\n            label: this.label\n        });\n    }\n    /**\n     *  Requests user attention to the window, this has no effect if the application\n     * is already focused. How requesting for user attention manifests is platform dependent,\n     * see `UserAttentionType` for details.\n     *\n     * Providing `null` will unset the request for user attention. Unsetting the request for\n     * user attention might not be done automatically by the WM when the window receives input.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** `null` has no effect.\n     * - **Linux:** Urgency levels have the same effect.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().requestUserAttention();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async requestUserAttention(requestType) {\n        let requestType_ = null;\n        if (requestType) {\n            if (requestType === UserAttentionType.Critical) {\n                requestType_ = { type: 'Critical' };\n            }\n            else {\n                requestType_ = { type: 'Informational' };\n            }\n        }\n        return invoke('plugin:window|request_user_attention', {\n            label: this.label,\n            value: requestType_\n        });\n    }\n    /**\n     * Updates the window resizable flag.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setResizable(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setResizable(resizable) {\n        return invoke('plugin:window|set_resizable', {\n            label: this.label,\n            value: resizable\n        });\n    }\n    /**\n     * Enable or disable the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setEnabled(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.0.0\n     */\n    async setEnabled(enabled) {\n        return invoke('plugin:window|set_enabled', {\n            label: this.label,\n            value: enabled\n        });\n    }\n    /**\n     * Whether the window is enabled or disabled.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setEnabled(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.0.0\n     */\n    async isEnabled() {\n        return invoke('plugin:window|is_enabled', {\n            label: this.label\n        });\n    }\n    /**\n     * Sets whether the window's native maximize button is enabled or not.\n     * If resizable is set to false, this setting is ignored.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Disables the \"zoom\" button in the window titlebar, which is also used to enter fullscreen mode.\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setMaximizable(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMaximizable(maximizable) {\n        return invoke('plugin:window|set_maximizable', {\n            label: this.label,\n            value: maximizable\n        });\n    }\n    /**\n     * Sets whether the window's native minimize button is enabled or not.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setMinimizable(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMinimizable(minimizable) {\n        return invoke('plugin:window|set_minimizable', {\n            label: this.label,\n            value: minimizable\n        });\n    }\n    /**\n     * Sets whether the window's native close button is enabled or not.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux:** GTK+ will do its best to convince the window manager not to show a close button. Depending on the system, this function may not have any effect when called on a window that is already visible\n     * - **iOS / Android:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setClosable(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setClosable(closable) {\n        return invoke('plugin:window|set_closable', {\n            label: this.label,\n            value: closable\n        });\n    }\n    /**\n     * Sets the window title.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setTitle('Tauri');\n     * ```\n     *\n     * @param title The new title\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setTitle(title) {\n        return invoke('plugin:window|set_title', {\n            label: this.label,\n            value: title\n        });\n    }\n    /**\n     * Maximizes the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().maximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async maximize() {\n        return invoke('plugin:window|maximize', {\n            label: this.label\n        });\n    }\n    /**\n     * Unmaximizes the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().unmaximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async unmaximize() {\n        return invoke('plugin:window|unmaximize', {\n            label: this.label\n        });\n    }\n    /**\n     * Toggles the window maximized state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().toggleMaximize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async toggleMaximize() {\n        return invoke('plugin:window|toggle_maximize', {\n            label: this.label\n        });\n    }\n    /**\n     * Minimizes the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().minimize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async minimize() {\n        return invoke('plugin:window|minimize', {\n            label: this.label\n        });\n    }\n    /**\n     * Unminimizes the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().unminimize();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async unminimize() {\n        return invoke('plugin:window|unminimize', {\n            label: this.label\n        });\n    }\n    /**\n     * Sets the window visibility to true.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().show();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async show() {\n        return invoke('plugin:window|show', {\n            label: this.label\n        });\n    }\n    /**\n     * Sets the window visibility to false.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().hide();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async hide() {\n        return invoke('plugin:window|hide', {\n            label: this.label\n        });\n    }\n    /**\n     * Closes the window.\n     *\n     * Note this emits a closeRequested event so you can intercept it. To force window close, use {@link Window.destroy}.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().close();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async close() {\n        return invoke('plugin:window|close', {\n            label: this.label\n        });\n    }\n    /**\n     * Destroys the window. Behaves like {@link Window.close} but forces the window close instead of emitting a closeRequested event.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().destroy();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async destroy() {\n        return invoke('plugin:window|destroy', {\n            label: this.label\n        });\n    }\n    /**\n     * Whether the window should have borders and bars.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setDecorations(false);\n     * ```\n     *\n     * @param decorations Whether the window should have borders and bars.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setDecorations(decorations) {\n        return invoke('plugin:window|set_decorations', {\n            label: this.label,\n            value: decorations\n        });\n    }\n    /**\n     * Whether or not the window should have shadow.\n     *\n     * #### Platform-specific\n     *\n     * - **Windows:**\n     *   - `false` has no effect on decorated window, shadows are always ON.\n     *   - `true` will make undecorated window have a 1px white border,\n     * and on Windows 11, it will have a rounded corners.\n     * - **Linux:** Unsupported.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setShadow(false);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setShadow(enable) {\n        return invoke('plugin:window|set_shadow', {\n            label: this.label,\n            value: enable\n        });\n    }\n    /**\n     * Set window effects.\n     */\n    async setEffects(effects) {\n        return invoke('plugin:window|set_effects', {\n            label: this.label,\n            value: effects\n        });\n    }\n    /**\n     * Clear any applied effects if possible.\n     */\n    async clearEffects() {\n        return invoke('plugin:window|set_effects', {\n            label: this.label,\n            value: null\n        });\n    }\n    /**\n     * Whether the window should always be on top of other windows.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setAlwaysOnTop(true);\n     * ```\n     *\n     * @param alwaysOnTop Whether the window should always be on top of other windows or not.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setAlwaysOnTop(alwaysOnTop) {\n        return invoke('plugin:window|set_always_on_top', {\n            label: this.label,\n            value: alwaysOnTop\n        });\n    }\n    /**\n     * Whether the window should always be below other windows.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setAlwaysOnBottom(true);\n     * ```\n     *\n     * @param alwaysOnBottom Whether the window should always be below other windows or not.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setAlwaysOnBottom(alwaysOnBottom) {\n        return invoke('plugin:window|set_always_on_bottom', {\n            label: this.label,\n            value: alwaysOnBottom\n        });\n    }\n    /**\n     * Prevents the window contents from being captured by other apps.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setContentProtected(true);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setContentProtected(protected_) {\n        return invoke('plugin:window|set_content_protected', {\n            label: this.label,\n            value: protected_\n        });\n    }\n    /**\n     * Resizes the window with a new inner size.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, LogicalSize } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSize(size) {\n        return invoke('plugin:window|set_size', {\n            label: this.label,\n            value: size instanceof Size ? size : new Size(size)\n        });\n    }\n    /**\n     * Sets the window minimum inner size. If the `size` argument is not provided, the constraint is unset.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, PhysicalSize } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setMinSize(new PhysicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size, or `null` to unset the constraint.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMinSize(size) {\n        return invoke('plugin:window|set_min_size', {\n            label: this.label,\n            value: size instanceof Size ? size : size ? new Size(size) : null\n        });\n    }\n    /**\n     * Sets the window maximum inner size. If the `size` argument is undefined, the constraint is unset.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, LogicalSize } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setMaxSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical inner size, or `null` to unset the constraint.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setMaxSize(size) {\n        return invoke('plugin:window|set_max_size', {\n            label: this.label,\n            value: size instanceof Size ? size : size ? new Size(size) : null\n        });\n    }\n    /**\n     * Sets the window inner size constraints.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setSizeConstraints({ minWidth: 300 });\n     * ```\n     *\n     * @param constraints The logical or physical inner size, or `null` to unset the constraint.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSizeConstraints(constraints) {\n        function logical(pixel) {\n            return pixel ? { Logical: pixel } : null;\n        }\n        return invoke('plugin:window|set_size_constraints', {\n            label: this.label,\n            value: {\n                minWidth: logical(constraints === null || constraints === void 0 ? void 0 : constraints.minWidth),\n                minHeight: logical(constraints === null || constraints === void 0 ? void 0 : constraints.minHeight),\n                maxWidth: logical(constraints === null || constraints === void 0 ? void 0 : constraints.maxWidth),\n                maxHeight: logical(constraints === null || constraints === void 0 ? void 0 : constraints.maxHeight)\n            }\n        });\n    }\n    /**\n     * Sets the window outer position.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, LogicalPosition } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setPosition(new LogicalPosition(600, 500));\n     * ```\n     *\n     * @param position The new position, in logical or physical pixels.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setPosition(position) {\n        return invoke('plugin:window|set_position', {\n            label: this.label,\n            value: position instanceof Position ? position : new Position(position)\n        });\n    }\n    /**\n     * Sets the window fullscreen state.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setFullscreen(true);\n     * ```\n     *\n     * @param fullscreen Whether the window should go to fullscreen or not.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFullscreen(fullscreen) {\n        return invoke('plugin:window|set_fullscreen', {\n            label: this.label,\n            value: fullscreen\n        });\n    }\n    /**\n     * Bring the window to front and focus.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setFocus();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFocus() {\n        return invoke('plugin:window|set_focus', {\n            label: this.label\n        });\n    }\n    /**\n     * Sets the window icon.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setIcon('/tauri/awesome.png');\n     * ```\n     *\n     * Note that you may need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     *\n     * @param icon Icon bytes or path to the icon file.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setIcon(icon) {\n        return invoke('plugin:window|set_icon', {\n            label: this.label,\n            value: transformImage(icon)\n        });\n    }\n    /**\n     * Whether the window icon should be hidden from the taskbar or not.\n     *\n     * #### Platform-specific\n     *\n     * - **macOS:** Unsupported.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setSkipTaskbar(true);\n     * ```\n     *\n     * @param skip true to hide window icon, false to show it.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSkipTaskbar(skip) {\n        return invoke('plugin:window|set_skip_taskbar', {\n            label: this.label,\n            value: skip\n        });\n    }\n    /**\n     * Grabs the cursor, preventing it from leaving the window.\n     *\n     * There's no guarantee that the cursor will be hidden. You should\n     * hide it by yourself if you want so.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux:** Unsupported.\n     * - **macOS:** This locks the cursor in a fixed location, which looks visually awkward.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setCursorGrab(true);\n     * ```\n     *\n     * @param grab `true` to grab the cursor icon, `false` to release it.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorGrab(grab) {\n        return invoke('plugin:window|set_cursor_grab', {\n            label: this.label,\n            value: grab\n        });\n    }\n    /**\n     * Modifies the cursor's visibility.\n     *\n     * #### Platform-specific\n     *\n     * - **Windows:** The cursor is only hidden within the confines of the window.\n     * - **macOS:** The cursor is hidden as long as the window has input focus, even if the cursor is\n     *   outside of the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setCursorVisible(false);\n     * ```\n     *\n     * @param visible If `false`, this will hide the cursor. If `true`, this will show the cursor.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorVisible(visible) {\n        return invoke('plugin:window|set_cursor_visible', {\n            label: this.label,\n            value: visible\n        });\n    }\n    /**\n     * Modifies the cursor icon of the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setCursorIcon('help');\n     * ```\n     *\n     * @param icon The new cursor icon.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorIcon(icon) {\n        return invoke('plugin:window|set_cursor_icon', {\n            label: this.label,\n            value: icon\n        });\n    }\n    /**\n     * Sets the window background color.\n     *\n     * #### Platform-specific:\n     *\n     * - **Windows:** alpha channel is ignored.\n     * - **iOS / Android:** Unsupported.\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.1.0\n     */\n    async setBackgroundColor(color) {\n        return invoke('plugin:window|set_background_color', { color });\n    }\n    /**\n     * Changes the position of the cursor in window coordinates.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, LogicalPosition } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setCursorPosition(new LogicalPosition(600, 300));\n     * ```\n     *\n     * @param position The new cursor position.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setCursorPosition(position) {\n        return invoke('plugin:window|set_cursor_position', {\n            label: this.label,\n            value: position instanceof Position ? position : new Position(position)\n        });\n    }\n    /**\n     * Changes the cursor events behavior.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setIgnoreCursorEvents(true);\n     * ```\n     *\n     * @param ignore `true` to ignore the cursor events; `false` to process them as usual.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setIgnoreCursorEvents(ignore) {\n        return invoke('plugin:window|set_ignore_cursor_events', {\n            label: this.label,\n            value: ignore\n        });\n    }\n    /**\n     * Starts dragging the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().startDragging();\n     * ```\n     *\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async startDragging() {\n        return invoke('plugin:window|start_dragging', {\n            label: this.label\n        });\n    }\n    /**\n     * Starts resize-dragging the window.\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().startResizeDragging();\n     * ```\n     *\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async startResizeDragging(direction) {\n        return invoke('plugin:window|start_resize_dragging', {\n            label: this.label,\n            value: direction\n        });\n    }\n    /**\n     * Sets the badge count. It is app wide and not specific to this window.\n     *\n     * #### Platform-specific\n     *\n     * - **Windows**: Unsupported. Use @{linkcode Window.setOverlayIcon} instead.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setBadgeCount(5);\n     * ```\n     *\n     * @param count The badge count. Use `undefined` to remove the badge.\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async setBadgeCount(count) {\n        return invoke('plugin:window|set_badge_count', {\n            label: this.label,\n            value: count\n        });\n    }\n    /**\n     * Sets the badge cont **macOS only**.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setBadgeLabel(\"Hello\");\n     * ```\n     *\n     * @param label The badge label. Use `undefined` to remove the badge.\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async setBadgeLabel(label) {\n        return invoke('plugin:window|set_badge_label', {\n            label: this.label,\n            value: label\n        });\n    }\n    /**\n     * Sets the overlay icon. **Windows only**\n     * The overlay icon can be set for every window.\n     *\n     *\n     * Note that you may need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     *\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setOverlayIcon(\"/tauri/awesome.png\");\n     * ```\n     *\n     * @param icon Icon bytes or path to the icon file. Use `undefined` to remove the overlay icon.\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async setOverlayIcon(icon) {\n        return invoke('plugin:window|set_overlay_icon', {\n            label: this.label,\n            value: icon ? transformImage(icon) : undefined\n        });\n    }\n    /**\n     * Sets the taskbar progress state.\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / macOS**: Progress bar is app-wide and not specific to this window.\n     * - **Linux**: Only supported desktop environments with `libunity` (e.g. GNOME).\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow, ProgressBarStatus } from '@tauri-apps/api/window';\n     * await getCurrentWindow().setProgressBar({\n     *   status: ProgressBarStatus.Normal,\n     *   progress: 50,\n     * });\n     * ```\n     *\n     * @return A promise indicating the success or failure of the operation.\n     */\n    async setProgressBar(state) {\n        return invoke('plugin:window|set_progress_bar', {\n            label: this.label,\n            value: state\n        });\n    }\n    /**\n     * Sets whether the window should be visible on all workspaces or virtual desktops.\n     *\n     * #### Platform-specific\n     *\n     * - **Windows / iOS / Android:** Unsupported.\n     *\n     * @since 2.0.0\n     */\n    async setVisibleOnAllWorkspaces(visible) {\n        return invoke('plugin:window|set_visible_on_all_workspaces', {\n            label: this.label,\n            value: visible\n        });\n    }\n    /**\n     * Sets the title bar style. **macOS only**.\n     *\n     * @since 2.0.0\n     */\n    async setTitleBarStyle(style) {\n        return invoke('plugin:window|set_title_bar_style', {\n            label: this.label,\n            value: style\n        });\n    }\n    /**\n     * Set window theme, pass in `null` or `undefined` to follow system theme\n     *\n     * #### Platform-specific\n     *\n     * - **Linux / macOS**: Theme is app-wide and not specific to this window.\n     * - **iOS / Android:** Unsupported.\n     *\n     * @since 2.0.0\n     */\n    async setTheme(theme) {\n        return invoke('plugin:window|set_theme', {\n            label: this.label,\n            value: theme\n        });\n    }\n    // Listeners\n    /**\n     * Listen to window resize.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await getCurrentWindow().onResized(({ payload: size }) => {\n     *  console.log('Window resized', size);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onResized(handler) {\n        return this.listen(TauriEvent.WINDOW_RESIZED, (e) => {\n            e.payload = new PhysicalSize(e.payload);\n            handler(e);\n        });\n    }\n    /**\n     * Listen to window move.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await getCurrentWindow().onMoved(({ payload: position }) => {\n     *  console.log('Window moved', position);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onMoved(handler) {\n        return this.listen(TauriEvent.WINDOW_MOVED, (e) => {\n            e.payload = new PhysicalPosition(e.payload);\n            handler(e);\n        });\n    }\n    /**\n     * Listen to window close requested. Emitted when the user requests to closes the window.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * import { confirm } from '@tauri-apps/api/dialog';\n     * const unlisten = await getCurrentWindow().onCloseRequested(async (event) => {\n     *   const confirmed = await confirm('Are you sure?');\n     *   if (!confirmed) {\n     *     // user did not confirm closing the window; let's prevent it\n     *     event.preventDefault();\n     *   }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onCloseRequested(handler) {\n        // eslint-disable-next-line @typescript-eslint/no-misused-promises\n        return this.listen(TauriEvent.WINDOW_CLOSE_REQUESTED, async (event) => {\n            const evt = new CloseRequestedEvent(event);\n            await handler(evt);\n            if (!evt.isPreventDefault()) {\n                await this.destroy();\n            }\n        });\n    }\n    /**\n     * Listen to a file drop event.\n     * The listener is triggered when the user hovers the selected files on the webview,\n     * drops the files or cancels the operation.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/webview\";\n     * const unlisten = await getCurrentWindow().onDragDropEvent((event) => {\n     *  if (event.payload.type === 'over') {\n     *    console.log('User hovering', event.payload.position);\n     *  } else if (event.payload.type === 'drop') {\n     *    console.log('User dropped', event.payload.paths);\n     *  } else {\n     *    console.log('File drop cancelled');\n     *  }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onDragDropEvent(handler) {\n        const unlistenDrag = await this.listen(TauriEvent.DRAG_ENTER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'enter',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragOver = await this.listen(TauriEvent.DRAG_OVER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'over',\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDrop = await this.listen(TauriEvent.DRAG_DROP, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'drop',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenCancel = await this.listen(TauriEvent.DRAG_LEAVE, (event) => {\n            handler({ ...event, payload: { type: 'leave' } });\n        });\n        return () => {\n            unlistenDrag();\n            unlistenDrop();\n            unlistenDragOver();\n            unlistenCancel();\n        };\n    }\n    /**\n     * Listen to window focus change.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await getCurrentWindow().onFocusChanged(({ payload: focused }) => {\n     *  console.log('Focus changed, window is focused? ' + focused);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onFocusChanged(handler) {\n        const unlistenFocus = await this.listen(TauriEvent.WINDOW_FOCUS, (event) => {\n            handler({ ...event, payload: true });\n        });\n        const unlistenBlur = await this.listen(TauriEvent.WINDOW_BLUR, (event) => {\n            handler({ ...event, payload: false });\n        });\n        return () => {\n            unlistenFocus();\n            unlistenBlur();\n        };\n    }\n    /**\n     * Listen to window scale change. Emitted when the window's scale factor has changed.\n     * The following user actions can cause DPI changes:\n     * - Changing the display's resolution.\n     * - Changing the display's scale factor (e.g. in Control Panel on Windows).\n     * - Moving the window to a display with a different scale factor.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await getCurrentWindow().onScaleChanged(({ payload }) => {\n     *  console.log('Scale changed', payload.scaleFactor, payload.size);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onScaleChanged(handler) {\n        return this.listen(TauriEvent.WINDOW_SCALE_FACTOR_CHANGED, handler);\n    }\n    /**\n     * Listen to the system theme change.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWindow } from \"@tauri-apps/api/window\";\n     * const unlisten = await getCurrentWindow().onThemeChanged(({ payload: theme }) => {\n     *  console.log('New theme: ' + theme);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onThemeChanged(handler) {\n        return this.listen(TauriEvent.WINDOW_THEME_CHANGED, handler);\n    }\n}\n/**\n * Background throttling policy\n *\n * @since 2.0.0\n */\nvar BackgroundThrottlingPolicy;\n(function (BackgroundThrottlingPolicy) {\n    BackgroundThrottlingPolicy[\"Disabled\"] = \"disabled\";\n    BackgroundThrottlingPolicy[\"Throttle\"] = \"throttle\";\n    BackgroundThrottlingPolicy[\"Suspend\"] = \"suspend\";\n})(BackgroundThrottlingPolicy || (BackgroundThrottlingPolicy = {}));\n/**\n * Platform-specific window effects\n *\n * @since 2.0.0\n */\nvar Effect;\n(function (Effect) {\n    /**\n     * A default material appropriate for the view's effectiveAppearance.  **macOS 10.14-**\n     *\n     * @deprecated since macOS 10.14. You should instead choose an appropriate semantic material.\n     */\n    Effect[\"AppearanceBased\"] = \"appearanceBased\";\n    /**\n     *  **macOS 10.14-**\n     *\n     * @deprecated since macOS 10.14. Use a semantic material instead.\n     */\n    Effect[\"Light\"] = \"light\";\n    /**\n     *  **macOS 10.14-**\n     *\n     * @deprecated since macOS 10.14. Use a semantic material instead.\n     */\n    Effect[\"Dark\"] = \"dark\";\n    /**\n     *  **macOS 10.14-**\n     *\n     * @deprecated since macOS 10.14. Use a semantic material instead.\n     */\n    Effect[\"MediumLight\"] = \"mediumLight\";\n    /**\n     *  **macOS 10.14-**\n     *\n     * @deprecated since macOS 10.14. Use a semantic material instead.\n     */\n    Effect[\"UltraDark\"] = \"ultraDark\";\n    /**\n     *  **macOS 10.10+**\n     */\n    Effect[\"Titlebar\"] = \"titlebar\";\n    /**\n     *  **macOS 10.10+**\n     */\n    Effect[\"Selection\"] = \"selection\";\n    /**\n     *  **macOS 10.11+**\n     */\n    Effect[\"Menu\"] = \"menu\";\n    /**\n     *  **macOS 10.11+**\n     */\n    Effect[\"Popover\"] = \"popover\";\n    /**\n     *  **macOS 10.11+**\n     */\n    Effect[\"Sidebar\"] = \"sidebar\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"HeaderView\"] = \"headerView\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"Sheet\"] = \"sheet\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"WindowBackground\"] = \"windowBackground\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"HudWindow\"] = \"hudWindow\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"FullScreenUI\"] = \"fullScreenUI\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"Tooltip\"] = \"tooltip\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"ContentBackground\"] = \"contentBackground\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"UnderWindowBackground\"] = \"underWindowBackground\";\n    /**\n     *  **macOS 10.14+**\n     */\n    Effect[\"UnderPageBackground\"] = \"underPageBackground\";\n    /**\n     *  **Windows 11 Only**\n     */\n    Effect[\"Mica\"] = \"mica\";\n    /**\n     * **Windows 7/10/11(22H1) Only**\n     *\n     * #### Notes\n     *\n     * This effect has bad performance when resizing/dragging the window on Windows 11 build 22621.\n     */\n    Effect[\"Blur\"] = \"blur\";\n    /**\n     * **Windows 10/11**\n     *\n     * #### Notes\n     *\n     * This effect has bad performance when resizing/dragging the window on Windows 10 v1903+ and Windows 11 build 22000.\n     */\n    Effect[\"Acrylic\"] = \"acrylic\";\n    /**\n     * Tabbed effect that matches the system dark perefence **Windows 11 Only**\n     */\n    Effect[\"Tabbed\"] = \"tabbed\";\n    /**\n     * Tabbed effect with dark mode but only if dark mode is enabled on the system **Windows 11 Only**\n     */\n    Effect[\"TabbedDark\"] = \"tabbedDark\";\n    /**\n     * Tabbed effect with light mode **Windows 11 Only**\n     */\n    Effect[\"TabbedLight\"] = \"tabbedLight\";\n})(Effect || (Effect = {}));\n/**\n * Window effect state **macOS only**\n *\n * @see https://developer.apple.com/documentation/appkit/nsvisualeffectview/state\n *\n * @since 2.0.0\n */\nvar EffectState;\n(function (EffectState) {\n    /**\n     *  Make window effect state follow the window's active state **macOS only**\n     */\n    EffectState[\"FollowsWindowActiveState\"] = \"followsWindowActiveState\";\n    /**\n     *  Make window effect state always active **macOS only**\n     */\n    EffectState[\"Active\"] = \"active\";\n    /**\n     *  Make window effect state always inactive **macOS only**\n     */\n    EffectState[\"Inactive\"] = \"inactive\";\n})(EffectState || (EffectState = {}));\nfunction mapMonitor(m) {\n    return m === null\n        ? null\n        : {\n            name: m.name,\n            scaleFactor: m.scaleFactor,\n            position: new PhysicalPosition(m.position),\n            size: new PhysicalSize(m.size),\n            workArea: {\n                position: new PhysicalPosition(m.workArea.position),\n                size: new PhysicalSize(m.workArea.size)\n            }\n        };\n}\n/**\n * Returns the monitor on which the window currently resides.\n * Returns `null` if current monitor can't be detected.\n * @example\n * ```typescript\n * import { currentMonitor } from '@tauri-apps/api/window';\n * const monitor = await currentMonitor();\n * ```\n *\n * @since 1.0.0\n */\nasync function currentMonitor() {\n    return invoke('plugin:window|current_monitor').then(mapMonitor);\n}\n/**\n * Returns the primary monitor of the system.\n * Returns `null` if it can't identify any monitor as a primary one.\n * @example\n * ```typescript\n * import { primaryMonitor } from '@tauri-apps/api/window';\n * const monitor = await primaryMonitor();\n * ```\n *\n * @since 1.0.0\n */\nasync function primaryMonitor() {\n    return invoke('plugin:window|primary_monitor').then(mapMonitor);\n}\n/**\n * Returns the monitor that contains the given point. Returns `null` if can't find any.\n * @example\n * ```typescript\n * import { monitorFromPoint } from '@tauri-apps/api/window';\n * const monitor = await monitorFromPoint(100.0, 200.0);\n * ```\n *\n * @since 1.0.0\n */\nasync function monitorFromPoint(x, y) {\n    return invoke('plugin:window|monitor_from_point', {\n        x,\n        y\n    }).then(mapMonitor);\n}\n/**\n * Returns the list of all the monitors available on the system.\n * @example\n * ```typescript\n * import { availableMonitors } from '@tauri-apps/api/window';\n * const monitors = await availableMonitors();\n * ```\n *\n * @since 1.0.0\n */\nasync function availableMonitors() {\n    return invoke('plugin:window|available_monitors').then((ms) => ms.map(mapMonitor));\n}\n/**\n * Get the cursor position relative to the top-left hand corner of the desktop.\n *\n * Note that the top-left hand corner of the desktop is not necessarily the same as the screen.\n * If the user uses a desktop with multiple monitors,\n * the top-left hand corner of the desktop is the top-left hand corner of the main monitor on Windows and macOS\n * or the top-left of the leftmost monitor on X11.\n *\n * The coordinates can be negative if the top-left hand corner of the window is outside of the visible screen region.\n */\nasync function cursorPosition() {\n    return invoke('plugin:window|cursor_position').then((v) => new PhysicalPosition(v));\n}\n\nexport { CloseRequestedEvent, Effect, EffectState, PhysicalPosition, PhysicalSize, ProgressBarStatus, UserAttentionType, Window, availableMonitors, currentMonitor, cursorPosition, getAllWindows, getCurrentWindow, monitorFromPoint, primaryMonitor };\n", "import { PhysicalPosition, PhysicalSize, Size, Position } from './dpi.js';\nimport { listen, once, emit, emitTo, TauriEvent } from './event.js';\nimport { invoke } from './core.js';\nimport { getCurrentWindow, Window } from './window.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Provides APIs to create webviews, communicate with other webviews and manipulate the current webview.\n *\n * #### Webview events\n *\n * Events can be listened to using {@link Webview.listen}:\n * ```typescript\n * import { getCurrentWebview } from \"@tauri-apps/api/webview\";\n * getCurrentWebview().listen(\"my-webview-event\", ({ event, payload }) => { });\n * ```\n *\n * @module\n */\n/**\n * Get an instance of `Webview` for the current webview.\n *\n * @since 2.0.0\n */\nfunction getCurrentWebview() {\n    return new Webview(getCurrentWindow(), window.__TAURI_INTERNALS__.metadata.currentWebview.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    });\n}\n/**\n * Gets a list of instances of `Webview` for all available webviews.\n *\n * @since 2.0.0\n */\nasync function getAllWebviews() {\n    return invoke('plugin:webview|get_all_webviews').then((webviews) => webviews.map((w) => new Webview(new Window(w.windowLabel, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    }), w.label, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    })));\n}\n/** @ignore */\n// events that are emitted right here instead of by the created webview\nconst localTauriEvents = ['tauri://created', 'tauri://error'];\n/**\n * Create new webview or get a handle to an existing one.\n *\n * Webviews are identified by a *label*  a unique identifier that can be used to reference it later.\n * It may only contain alphanumeric characters `a-zA-Z` plus the following special characters `-`, `/`, `:` and `_`.\n *\n * @example\n * ```typescript\n * import { Window } from \"@tauri-apps/api/window\"\n * import { Webview } from \"@tauri-apps/api/webview\"\n *\n * const appWindow = new Window('uniqueLabel');\n *\n * appWindow.once('tauri://created', async function () {\n *   // `new Webview` Should be called after the window is successfully created,\n *   // or webview may not be attached to the window since window is not created yet.\n *\n *   // loading embedded asset:\n *   const webview = new Webview(appWindow, 'theUniqueLabel', {\n *     url: 'path/to/page.html',\n *\n *     // create a webview with specific logical position and size\n *     x: 0,\n *     y: 0,\n *     width: 800,\n *     height: 600,\n *   });\n *   // alternatively, load a remote URL:\n *   const webview = new Webview(appWindow, 'theUniqueLabel', {\n *     url: 'https://github.com/tauri-apps/tauri',\n *\n *     // create a webview with specific logical position and size\n *     x: 0,\n *     y: 0,\n *     width: 800,\n *     height: 600,\n *   });\n *\n *   webview.once('tauri://created', function () {\n *     // webview successfully created\n *   });\n *   webview.once('tauri://error', function (e) {\n *     // an error happened creating the webview\n *   });\n *\n *\n *   // emit an event to the backend\n *   await webview.emit(\"some-event\", \"data\");\n *   // listen to an event from the backend\n *   const unlisten = await webview.listen(\"event-name\", e => { });\n *   unlisten();\n * });\n * ```\n *\n * @since 2.0.0\n */\nclass Webview {\n    /**\n     * Creates a new Webview.\n     * @example\n     * ```typescript\n     * import { Window } from '@tauri-apps/api/window'\n     * import { Webview } from '@tauri-apps/api/webview'\n     * const appWindow = new Window('my-label')\n     *\n     * appWindow.once('tauri://created', async function() {\n     *   const webview = new Webview(appWindow, 'my-label', {\n     *     url: 'https://github.com/tauri-apps/tauri',\n     *\n     *     // create a webview with specific logical position and size\n     *     x: 0,\n     *     y: 0,\n     *     width: 800,\n     *     height: 600,\n     *   });\n     *\n     *   webview.once('tauri://created', function () {\n     *     // webview successfully created\n     *   });\n     *   webview.once('tauri://error', function (e) {\n     *     // an error happened creating the webview\n     *   });\n     * });\n     * ```\n     *\n     * @param window the window to add this webview to.\n     * @param label The unique webview label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The {@link Webview} instance to communicate with the webview.\n     */\n    constructor(window, label, options) {\n        this.window = window;\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WebviewOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invoke('plugin:webview|create_webview', {\n                windowLabel: window.label,\n                options: {\n                    ...options,\n                    label\n                }\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the Webview for the webview associated with the given label.\n     * @example\n     * ```typescript\n     * import { Webview } from '@tauri-apps/api/webview';\n     * const mainWebview = Webview.getByLabel('main');\n     * ```\n     *\n     * @param label The webview label.\n     * @returns The Webview instance to communicate with the webview or null if the webview doesn't exist.\n     */\n    static async getByLabel(label) {\n        var _a;\n        return (_a = (await getAllWebviews()).find((w) => w.label === label)) !== null && _a !== void 0 ? _a : null;\n    }\n    /**\n     * Get an instance of `Webview` for the current webview.\n     */\n    static getCurrent() {\n        return getCurrentWebview();\n    }\n    /**\n     * Gets a list of instances of `Webview` for all available webviews.\n     */\n    static async getAll() {\n        return getAllWebviews();\n    }\n    /**\n     * Listen to an emitted event on this webview.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const unlisten = await getCurrentWebview().listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return listen(event, handler, {\n            target: { kind: 'Webview', label: this.label }\n        });\n    }\n    /**\n     * Listen to an emitted event on this webview only once.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const unlisten = await getCurrent().once<null>('initialized', (event) => {\n     *   console.log(`Webview initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return once(event, handler, {\n            target: { kind: 'Webview', label: this.label }\n        });\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets}.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().emit('webview-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emit(event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emit(event, payload);\n    }\n    /**\n     * Emits an event to all {@link EventTarget|targets} matching the given target.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().emitTo('main', 'webview-loaded', { loggedIn: true, token: 'authToken' });\n     * ```\n     *\n     * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param payload Event payload.\n     */\n    async emitTo(target, event, payload) {\n        if (localTauriEvents.includes(event)) {\n            // eslint-disable-next-line\n            for (const handler of this.listeners[event] || []) {\n                handler({\n                    event,\n                    id: -1,\n                    payload\n                });\n            }\n            return;\n        }\n        return emitTo(target, event, payload);\n    }\n    /** @ignore */\n    _handleTauriEvent(event, handler) {\n        if (localTauriEvents.includes(event)) {\n            if (!(event in this.listeners)) {\n                // eslint-disable-next-line security/detect-object-injection\n                this.listeners[event] = [handler];\n            }\n            else {\n                // eslint-disable-next-line security/detect-object-injection\n                this.listeners[event].push(handler);\n            }\n            return true;\n        }\n        return false;\n    }\n    // Getters\n    /**\n     * The position of the top-left hand corner of the webview's client area relative to the top-left hand corner of the desktop.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const position = await getCurrentWebview().position();\n     * ```\n     *\n     * @returns The webview's position.\n     */\n    async position() {\n        return invoke('plugin:webview|webview_position', {\n            label: this.label\n        }).then((p) => new PhysicalPosition(p));\n    }\n    /**\n     * The physical size of the webview's client area.\n     * The client area is the content of the webview, excluding the title bar and borders.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * const size = await getCurrentWebview().size();\n     * ```\n     *\n     * @returns The webview's size.\n     */\n    async size() {\n        return invoke('plugin:webview|webview_size', {\n            label: this.label\n        }).then((s) => new PhysicalSize(s));\n    }\n    // Setters\n    /**\n     * Closes the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().close();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async close() {\n        return invoke('plugin:webview|webview_close', {\n            label: this.label\n        });\n    }\n    /**\n     * Resizes the webview.\n     * @example\n     * ```typescript\n     * import { getCurrent, LogicalSize } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setSize(new LogicalSize(600, 500));\n     * ```\n     *\n     * @param size The logical or physical size.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setSize(size) {\n        return invoke('plugin:webview|set_webview_size', {\n            label: this.label,\n            value: size instanceof Size ? size : new Size(size)\n        });\n    }\n    /**\n     * Sets the webview position.\n     * @example\n     * ```typescript\n     * import { getCurrent, LogicalPosition } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setPosition(new LogicalPosition(600, 500));\n     * ```\n     *\n     * @param position The new position, in logical or physical pixels.\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setPosition(position) {\n        return invoke('plugin:webview|set_webview_position', {\n            label: this.label,\n            value: position instanceof Position ? position : new Position(position)\n        });\n    }\n    /**\n     * Bring the webview to front and focus.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setFocus();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setFocus() {\n        return invoke('plugin:webview|set_webview_focus', {\n            label: this.label\n        });\n    }\n    /**\n     * Sets whether the webview should automatically grow and shrink its size and position when the parent window resizes.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setAutoResize(true);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setAutoResize(autoResize) {\n        return invoke('plugin:webview|set_webview_auto_resize', {\n            label: this.label,\n            value: autoResize\n        });\n    }\n    /**\n     * Hide the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().hide();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async hide() {\n        return invoke('plugin:webview|webview_hide', {\n            label: this.label\n        });\n    }\n    /**\n     * Show the webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().show();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async show() {\n        return invoke('plugin:webview|webview_show', {\n            label: this.label\n        });\n    }\n    /**\n     * Set webview zoom level.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().setZoom(1.5);\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async setZoom(scaleFactor) {\n        return invoke('plugin:webview|set_webview_zoom', {\n            label: this.label,\n            value: scaleFactor\n        });\n    }\n    /**\n     * Moves this webview to the given label.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().reparent('other-window');\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async reparent(window) {\n        return invoke('plugin:webview|reparent', {\n            label: this.label,\n            window: typeof window === 'string' ? window : window.label\n        });\n    }\n    /**\n     * Clears all browsing data for this webview.\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from '@tauri-apps/api/webview';\n     * await getCurrentWebview().clearAllBrowsingData();\n     * ```\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     */\n    async clearAllBrowsingData() {\n        return invoke('plugin:webview|clear_all_browsing_data');\n    }\n    /**\n     * Specify the webview background color.\n     *\n     * #### Platfrom-specific:\n     *\n     * - **macOS / iOS**: Not implemented.\n     * - **Windows**:\n     *   - On Windows 7, transparency is not supported and the alpha value will be ignored.\n     *   - On Windows higher than 7: translucent colors are not supported so any alpha value other than `0` will be replaced by `255`\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.1.0\n     */\n    async setBackgroundColor(color) {\n        return invoke('plugin:webview|set_webview_background_color', { color });\n    }\n    // Listeners\n    /**\n     * Listen to a file drop event.\n     * The listener is triggered when the user hovers the selected files on the webview,\n     * drops the files or cancels the operation.\n     *\n     * @example\n     * ```typescript\n     * import { getCurrentWebview } from \"@tauri-apps/api/webview\";\n     * const unlisten = await getCurrentWebview().onDragDropEvent((event) => {\n     *  if (event.payload.type === 'over') {\n     *    console.log('User hovering', event.payload.position);\n     *  } else if (event.payload.type === 'drop') {\n     *    console.log('User dropped', event.payload.paths);\n     *  } else {\n     *    console.log('File drop cancelled');\n     *  }\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * When the debugger panel is open, the drop position of this event may be inaccurate due to a known limitation.\n     * To retrieve the correct drop position, please detach the debugger.\n     *\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async onDragDropEvent(handler) {\n        const unlistenDragEnter = await this.listen(TauriEvent.DRAG_ENTER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'enter',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragOver = await this.listen(TauriEvent.DRAG_OVER, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'over',\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragDrop = await this.listen(TauriEvent.DRAG_DROP, (event) => {\n            handler({\n                ...event,\n                payload: {\n                    type: 'drop',\n                    paths: event.payload.paths,\n                    position: new PhysicalPosition(event.payload.position)\n                }\n            });\n        });\n        const unlistenDragLeave = await this.listen(TauriEvent.DRAG_LEAVE, (event) => {\n            handler({ ...event, payload: { type: 'leave' } });\n        });\n        return () => {\n            unlistenDragEnter();\n            unlistenDragDrop();\n            unlistenDragOver();\n            unlistenDragLeave();\n        };\n    }\n}\n\nexport { Webview, getAllWebviews, getCurrentWebview };\n", "import { getCurrentWebview, Webview } from './webview.js';\nimport { Window } from './window.js';\nimport { listen, once } from './event.js';\nimport { invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Get an instance of `Webview` for the current webview window.\n *\n * @since 2.0.0\n */\nfunction getCurrentWebviewWindow() {\n    const webview = getCurrentWebview();\n    // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n    return new WebviewWindow(webview.label, { skip: true });\n}\n/**\n * Gets a list of instances of `Webview` for all available webview windows.\n *\n * @since 2.0.0\n */\nasync function getAllWebviewWindows() {\n    return invoke('plugin:window|get_all_windows').then((windows) => windows.map((w) => new WebviewWindow(w, {\n        // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n        skip: true\n    })));\n}\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nclass WebviewWindow {\n    /**\n     * Creates a new {@link Window} hosting a {@link Webview}.\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow'\n     * const webview = new WebviewWindow('my-label', {\n     *   url: 'https://github.com/tauri-apps/tauri'\n     * });\n     * webview.once('tauri://created', function () {\n     *  // webview successfully created\n     * });\n     * webview.once('tauri://error', function (e) {\n     *  // an error happened creating the webview\n     * });\n     * ```\n     *\n     * @param label The unique webview label. Must be alphanumeric: `a-zA-Z-/:_`.\n     * @returns The {@link WebviewWindow} instance to communicate with the window and webview.\n     */\n    constructor(label, options = {}) {\n        var _a;\n        this.label = label;\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        this.listeners = Object.create(null);\n        // @ts-expect-error `skip` is not a public API so it is not defined in WebviewOptions\n        if (!(options === null || options === void 0 ? void 0 : options.skip)) {\n            invoke('plugin:webview|create_webview_window', {\n                options: {\n                    ...options,\n                    parent: typeof options.parent === 'string'\n                        ? options.parent\n                        : (_a = options.parent) === null || _a === void 0 ? void 0 : _a.label,\n                    label\n                }\n            })\n                .then(async () => this.emit('tauri://created'))\n                .catch(async (e) => this.emit('tauri://error', e));\n        }\n    }\n    /**\n     * Gets the Webview for the webview associated with the given label.\n     * @example\n     * ```typescript\n     * import { Webview } from '@tauri-apps/api/webviewWindow';\n     * const mainWebview = Webview.getByLabel('main');\n     * ```\n     *\n     * @param label The webview label.\n     * @returns The Webview instance to communicate with the webview or null if the webview doesn't exist.\n     */\n    static async getByLabel(label) {\n        var _a;\n        const webview = (_a = (await getAllWebviewWindows()).find((w) => w.label === label)) !== null && _a !== void 0 ? _a : null;\n        if (webview) {\n            // @ts-expect-error `skip` is not defined in the public API but it is handled by the constructor\n            return new WebviewWindow(webview.label, { skip: true });\n        }\n        return null;\n    }\n    /**\n     * Get an instance of `Webview` for the current webview.\n     */\n    static getCurrent() {\n        return getCurrentWebviewWindow();\n    }\n    /**\n     * Gets a list of instances of `Webview` for all available webviews.\n     */\n    static async getAll() {\n        return getAllWebviewWindows();\n    }\n    /**\n     * Listen to an emitted event on this webivew window.\n     *\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow';\n     * const unlisten = await WebviewWindow.getCurrent().listen<string>('state-changed', (event) => {\n     *   console.log(`Got error: ${payload}`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async listen(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return listen(event, handler, {\n            target: { kind: 'WebviewWindow', label: this.label }\n        });\n    }\n    /**\n     * Listen to an emitted event on this webview window only once.\n     *\n     * @example\n     * ```typescript\n     * import { WebviewWindow } from '@tauri-apps/api/webviewWindow';\n     * const unlisten = await WebviewWindow.getCurrent().once<null>('initialized', (event) => {\n     *   console.log(`Webview initialized!`);\n     * });\n     *\n     * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n     * unlisten();\n     * ```\n     *\n     * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n     * @param handler Event handler.\n     * @returns A promise resolving to a function to unlisten to the event.\n     * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n     */\n    async once(event, handler) {\n        if (this._handleTauriEvent(event, handler)) {\n            return () => {\n                // eslint-disable-next-line security/detect-object-injection\n                const listeners = this.listeners[event];\n                listeners.splice(listeners.indexOf(handler), 1);\n            };\n        }\n        return once(event, handler, {\n            target: { kind: 'WebviewWindow', label: this.label }\n        });\n    }\n    /**\n     * Set the window and webview background color.\n     *\n     * #### Platform-specific:\n     *\n     * - **Android / iOS:** Unsupported for the window layer.\n     * - **macOS / iOS**: Not implemented for the webview layer.\n     * - **Windows**:\n     *   - alpha channel is ignored for the window layer.\n     *   - On Windows 7, alpha channel is ignored for the webview layer.\n     *   - On Windows 8 and newer, if alpha channel is not `0`, it will be ignored.\n     *\n     * @returns A promise indicating the success or failure of the operation.\n     *\n     * @since 2.1.0\n     */\n    async setBackgroundColor(color) {\n        return invoke('plugin:window|set_background_color', { color }).then(() => {\n            return invoke('plugin:webview|set_webview_background_color', { color });\n        });\n    }\n}\n// Order matters, we use window APIs by default\napplyMixins(WebviewWindow, [Window, Webview]);\n/** Extends a base class by other specified classes, without overriding existing properties */\nfunction applyMixins(baseClass, extendedClasses) {\n    (Array.isArray(extendedClasses)\n        ? extendedClasses\n        : [extendedClasses]).forEach((extendedClass) => {\n        Object.getOwnPropertyNames(extendedClass.prototype).forEach((name) => {\n            var _a;\n            if (typeof baseClass.prototype === 'object'\n                && baseClass.prototype\n                && name in baseClass.prototype)\n                return;\n            Object.defineProperty(baseClass.prototype, name, \n            // eslint-disable-next-line\n            (_a = Object.getOwnPropertyDescriptor(extendedClass.prototype, name)) !== null && _a !== void 0 ? _a : Object.create(null));\n        });\n    });\n}\n\nexport { WebviewWindow, getAllWebviewWindows, getCurrentWebviewWindow };\n"], "mappings": ";;;;;;;;;AAcA,IAAM,cAAN,MAAkB;AAAA,EACd,eAAe,MAAM;AACjB,SAAK,OAAO;AACZ,QAAI,KAAK,WAAW,GAAG;AACnB,UAAI,aAAa,KAAK,CAAC,GAAG;AACtB,aAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ;AAC7B,aAAK,SAAS,KAAK,CAAC,EAAE,QAAQ;AAAA,MAClC,OACK;AACD,aAAK,QAAQ,KAAK,CAAC,EAAE;AACrB,aAAK,SAAS,KAAK,CAAC,EAAE;AAAA,MAC1B;AAAA,IACJ,OACK;AACD,WAAK,QAAQ,KAAK,CAAC;AACnB,WAAK,SAAS,KAAK,CAAC;AAAA,IACxB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,WAAW,aAAa;AACpB,WAAO,IAAI,aAAa,KAAK,QAAQ,aAAa,KAAK,SAAS,WAAW;AAAA,EAC/E;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AAYA,IAAM,eAAN,MAAmB;AAAA,EACf,eAAe,MAAM;AACjB,SAAK,OAAO;AACZ,QAAI,KAAK,WAAW,GAAG;AACnB,UAAI,cAAc,KAAK,CAAC,GAAG;AACvB,aAAK,QAAQ,KAAK,CAAC,EAAE,SAAS;AAC9B,aAAK,SAAS,KAAK,CAAC,EAAE,SAAS;AAAA,MACnC,OACK;AACD,aAAK,QAAQ,KAAK,CAAC,EAAE;AACrB,aAAK,SAAS,KAAK,CAAC,EAAE;AAAA,MAC1B;AAAA,IACJ,OACK;AACD,WAAK,QAAQ,KAAK,CAAC;AACnB,WAAK,SAAS,KAAK,CAAC;AAAA,IACxB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,aAAa;AACnB,WAAO,IAAI,YAAY,KAAK,QAAQ,aAAa,KAAK,SAAS,WAAW;AAAA,EAC9E;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AAgCA,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,MAAM;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,UAAU,aAAa;AACnB,WAAO,KAAK,gBAAgB,cACtB,KAAK,OACL,KAAK,KAAK,UAAU,WAAW;AAAA,EACzC;AAAA,EACA,WAAW,aAAa;AACpB,WAAO,KAAK,gBAAgB,eACtB,KAAK,OACL,KAAK,KAAK,WAAW,WAAW;AAAA,EAC1C;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,CAAC,GAAG,KAAK,KAAK,IAAI,EAAE,GAAG;AAAA,QACnB,OAAO,KAAK,KAAK;AAAA,QACjB,QAAQ,KAAK,KAAK;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AAOA,IAAM,kBAAN,MAAsB;AAAA,EAClB,eAAe,MAAM;AACjB,SAAK,OAAO;AACZ,QAAI,KAAK,WAAW,GAAG;AACnB,UAAI,aAAa,KAAK,CAAC,GAAG;AACtB,aAAK,IAAI,KAAK,CAAC,EAAE,QAAQ;AACzB,aAAK,IAAI,KAAK,CAAC,EAAE,QAAQ;AAAA,MAC7B,OACK;AACD,aAAK,IAAI,KAAK,CAAC,EAAE;AACjB,aAAK,IAAI,KAAK,CAAC,EAAE;AAAA,MACrB;AAAA,IACJ,OACK;AACD,WAAK,IAAI,KAAK,CAAC;AACf,WAAK,IAAI,KAAK,CAAC;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,WAAW,aAAa;AACpB,WAAO,IAAI,iBAAiB,KAAK,IAAI,aAAa,KAAK,IAAI,WAAW;AAAA,EAC1E;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AAQA,IAAM,mBAAN,MAAuB;AAAA,EACnB,eAAe,MAAM;AACjB,SAAK,OAAO;AACZ,QAAI,KAAK,WAAW,GAAG;AACnB,UAAI,cAAc,KAAK,CAAC,GAAG;AACvB,aAAK,IAAI,KAAK,CAAC,EAAE,SAAS;AAC1B,aAAK,IAAI,KAAK,CAAC,EAAE,SAAS;AAAA,MAC9B,OACK;AACD,aAAK,IAAI,KAAK,CAAC,EAAE;AACjB,aAAK,IAAI,KAAK,CAAC,EAAE;AAAA,MACrB;AAAA,IACJ,OACK;AACD,WAAK,IAAI,KAAK,CAAC;AACf,WAAK,IAAI,KAAK,CAAC;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,UAAU,aAAa;AACnB,WAAO,IAAI,gBAAgB,KAAK,IAAI,aAAa,KAAK,IAAI,WAAW;AAAA,EACzE;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AAgCA,IAAM,WAAN,MAAe;AAAA,EACX,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,UAAU,aAAa;AACnB,WAAO,KAAK,oBAAoB,kBAC1B,KAAK,WACL,KAAK,SAAS,UAAU,WAAW;AAAA,EAC7C;AAAA,EACA,WAAW,aAAa;AACpB,WAAO,KAAK,oBAAoB,mBAC1B,KAAK,WACL,KAAK,SAAS,WAAW,WAAW;AAAA,EAC9C;AAAA,EACA,CAAC,mBAAmB,IAAI;AACpB,WAAO;AAAA,MACH,CAAC,GAAG,KAAK,SAAS,IAAI,EAAE,GAAG;AAAA,QACvB,GAAG,KAAK,SAAS;AAAA,QACjB,GAAG,KAAK,SAAS;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;;;ACnUA,IAAI;AAAA,CACH,SAAUA,aAAY;AACnB,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,6BAA6B,IAAI;AAC5C,EAAAA,YAAW,sBAAsB,IAAI;AACrC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,iBAAiB,IAAI;AAChC,EAAAA,YAAW,YAAY,IAAI;AAC3B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,YAAY,IAAI;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;AASlC,eAAe,UAAU,OAAO,SAAS;AACrC,SAAO,iCAAiC,mBAAmB,OAAO,OAAO;AACzE,QAAM,OAAO,yBAAyB;AAAA,IAClC;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAuBA,eAAe,OAAO,OAAO,SAAS,SAAS;AAC3C,MAAI;AACJ,QAAM,SAAS,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,WACvF,EAAE,MAAM,YAAY,OAAO,QAAQ,OAAO,KACxC,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK,EAAE,MAAM,MAAM;AAC9H,SAAO,OAAO,uBAAuB;AAAA,IACjC;AAAA,IACA;AAAA,IACA,SAAS,kBAAkB,OAAO;AAAA,EACtC,CAAC,EAAE,KAAK,CAAC,YAAY;AACjB,WAAO,YAAY,UAAU,OAAO,OAAO;AAAA,EAC/C,CAAC;AACL;AA2BA,eAAe,KAAK,OAAO,SAAS,SAAS;AACzC,SAAO,OAAO,OAAO,CAAC,cAAc;AAChC,SAAK,UAAU,OAAO,UAAU,EAAE;AAClC,YAAQ,SAAS;AAAA,EACrB,GAAG,OAAO;AACd;AAeA,eAAe,KAAK,OAAO,SAAS;AAChC,QAAM,OAAO,qBAAqB;AAAA,IAC9B;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAgBA,eAAe,OAAO,QAAQ,OAAO,SAAS;AAC1C,QAAM,cAAc,OAAO,WAAW,WAAW,EAAE,MAAM,YAAY,OAAO,OAAO,IAAI;AACvF,QAAM,OAAO,wBAAwB;AAAA,IACjC,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACJ,CAAC;AACL;;;ACrJA,IAAM,QAAN,MAAM,eAAc,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,YAAY,KAAK;AACb,UAAM,GAAG;AAAA,EACb;AAAA;AAAA,EAEA,aAAa,IAAI,MAAM,OAAO,QAAQ;AAClC,WAAO,OAAO,oBAAoB;AAAA,MAC9B,MAAM,eAAe,IAAI;AAAA,MACzB;AAAA,MACA;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,aAAa,UAAU,OAAO;AAC1B,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,eAAe,KAAK;AAAA,IAC/B,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,SAAS,MAAM;AACxB,WAAO,OAAO,0BAA0B,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EAClF;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB;AAAA,MAC/B,KAAK,KAAK;AAAA,IACd,CAAC,EAAE,KAAK,CAAC,WAAW,IAAI,WAAW,MAAM,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD;AACJ;AAOA,SAAS,eAAe,OAAO;AAC3B,QAAM,MAAM,SAAS,OACf,OACA,OAAO,UAAU,WACb,QACA,iBAAiB,QACb,MAAM,MACN;AACd,SAAO;AACX;;;ACvDA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAM1B,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AAMvD,EAAAA,mBAAkBA,mBAAkB,eAAe,IAAI,CAAC,IAAI;AAChE,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,OAAO;AACf,SAAK,kBAAkB;AACvB,SAAK,QAAQ,MAAM;AACnB,SAAK,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,iBAAiB;AACb,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAI1B,EAAAA,mBAAkB,MAAM,IAAI;AAI5B,EAAAA,mBAAkB,QAAQ,IAAI;AAI9B,EAAAA,mBAAkB,eAAe,IAAI;AAIrC,EAAAA,mBAAkB,QAAQ,IAAI;AAI9B,EAAAA,mBAAkB,OAAO,IAAI;AACjC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,SAAS,mBAAmB;AACxB,SAAO,IAAI,OAAO,OAAO,oBAAoB,SAAS,cAAc,OAAO;AAAA;AAAA,IAEvE,MAAM;AAAA,EACV,CAAC;AACL;AAMA,eAAe,gBAAgB;AAC3B,SAAO,OAAO,+BAA+B,EAAE,KAAK,CAAC,YAAY,QAAQ,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG;AAAA;AAAA,IAE9F,MAAM;AAAA,EACV,CAAC,CAAC,CAAC;AACP;AAGA,IAAM,mBAAmB,CAAC,mBAAmB,eAAe;AA6B5D,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBT,YAAY,OAAO,UAAU,CAAC,GAAG;AAC7B,QAAI;AACJ,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAEnC,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,aAAO,wBAAwB;AAAA,QAC3B,SAAS;AAAA,UACL,GAAG;AAAA,UACH,QAAQ,OAAO,QAAQ,WAAW,WAC5B,QAAQ,UACP,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,UACpE;AAAA,QACJ;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,WAAW,OAAO;AAC3B,QAAI;AACJ,YAAQ,MAAM,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,aAAa;AAChB,WAAO,iBAAiB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,WAAO,cAAc;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa,mBAAmB;AAC5B,eAAW,KAAK,MAAM,cAAc,GAAG;AACnC,UAAI,MAAM,EAAE,UAAU,GAAG;AACrB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B,QAAQ,EAAE,MAAM,UAAU,OAAO,KAAK,MAAM;AAAA,IAChD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,SAAS;AAAA,MACxB,QAAQ,EAAE,MAAM,UAAU,OAAO,KAAK,MAAM;AAAA,IAChD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,OAAO,QAAQ,OAAO,SAAS;AACjC,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,OAAO,QAAQ,OAAO,OAAO;AAAA,EACxC;AAAA;AAAA,EAEA,kBAAkB,OAAO,SAAS;AAC9B,QAAI,iBAAiB,SAAS,KAAK,GAAG;AAClC,UAAI,EAAE,SAAS,KAAK,YAAY;AAE5B,aAAK,UAAU,KAAK,IAAI,CAAC,OAAO;AAAA,MACpC,OACK;AAED,aAAK,UAAU,KAAK,EAAE,KAAK,OAAO;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc;AAChB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY;AACd,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY;AACd,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,eAAe;AACjB,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,cAAc;AAChB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY;AACd,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,gBAAgB;AAClB,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,gBAAgB;AAClB,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,aAAa;AACf,WAAO,OAAO,6BAA6B;AAAA,MACvC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY;AACd,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,QAAQ;AACV,WAAO,OAAO,uBAAuB;AAAA,MACjC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,QAAQ;AACV,WAAO,OAAO,uBAAuB;AAAA,MACjC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,OAAO,kCAAkC;AAAA,MAC5C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS;AACX,WAAO,OAAO,wBAAwB;AAAA,MAClC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,qBAAqB,aAAa;AACpC,QAAI,eAAe;AACnB,QAAI,aAAa;AACb,UAAI,gBAAgB,kBAAkB,UAAU;AAC5C,uBAAe,EAAE,MAAM,WAAW;AAAA,MACtC,OACK;AACD,uBAAe,EAAE,MAAM,gBAAgB;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO,OAAO,wCAAwC;AAAA,MAClD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa,WAAW;AAC1B,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,WAAW,SAAS;AACtB,WAAO,OAAO,6BAA6B;AAAA,MACvC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,YAAY;AACd,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,eAAe,aAAa;AAC9B,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,eAAe,aAAa;AAC9B,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,YAAY,UAAU;AACxB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS,OAAO;AAClB,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,OAAO,0BAA0B;AAAA,MACpC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa;AACf,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB;AACnB,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,OAAO,0BAA0B;AAAA,MACpC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa;AACf,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,sBAAsB;AAAA,MAChC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,sBAAsB;AAAA,MAChC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,QAAQ;AACV,WAAO,OAAO,uBAAuB;AAAA,MACjC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,UAAU;AACZ,WAAO,OAAO,yBAAyB;AAAA,MACnC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,aAAa;AAC9B,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,UAAU,QAAQ;AACpB,WAAO,OAAO,4BAA4B;AAAA,MACtC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,WAAW,SAAS;AACtB,WAAO,OAAO,6BAA6B;AAAA,MACvC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,eAAe;AACjB,WAAO,OAAO,6BAA6B;AAAA,MACvC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,aAAa;AAC9B,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,kBAAkB,gBAAgB;AACpC,WAAO,OAAO,sCAAsC;AAAA,MAChD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,oBAAoB,YAAY;AAClC,WAAO,OAAO,uCAAuC;AAAA,MACjD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ,MAAM;AAChB,WAAO,OAAO,0BAA0B;AAAA,MACpC,OAAO,KAAK;AAAA,MACZ,OAAO,gBAAgB,OAAO,OAAO,IAAI,KAAK,IAAI;AAAA,IACtD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW,MAAM;AACnB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,MACZ,OAAO,gBAAgB,OAAO,OAAO,OAAO,IAAI,KAAK,IAAI,IAAI;AAAA,IACjE,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW,MAAM;AACnB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,MACZ,OAAO,gBAAgB,OAAO,OAAO,OAAO,IAAI,KAAK,IAAI,IAAI;AAAA,IACjE,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,mBAAmB,aAAa;AAClC,aAAS,QAAQ,OAAO;AACpB,aAAO,QAAQ,EAAE,SAAS,MAAM,IAAI;AAAA,IACxC;AACA,WAAO,OAAO,sCAAsC;AAAA,MAChD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,QACH,UAAU,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,QAAQ;AAAA,QAChG,WAAW,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS;AAAA,QAClG,UAAU,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,QAAQ;AAAA,QAChG,WAAW,QAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS;AAAA,MACtG;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,UAAU;AACxB,WAAO,OAAO,8BAA8B;AAAA,MACxC,OAAO,KAAK;AAAA,MACZ,OAAO,oBAAoB,WAAW,WAAW,IAAI,SAAS,QAAQ;AAAA,IAC1E,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,YAAY;AAC5B,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,QAAQ,MAAM;AAChB,WAAO,OAAO,0BAA0B;AAAA,MACpC,OAAO,KAAK;AAAA,MACZ,OAAO,eAAe,IAAI;AAAA,IAC9B,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,eAAe,MAAM;AACvB,WAAO,OAAO,kCAAkC;AAAA,MAC5C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,cAAc,MAAM;AACtB,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,iBAAiB,SAAS;AAC5B,WAAO,OAAO,oCAAoC;AAAA,MAC9C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,MAAM;AACtB,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,mBAAmB,OAAO;AAC5B,WAAO,OAAO,sCAAsC,EAAE,MAAM,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,kBAAkB,UAAU;AAC9B,WAAO,OAAO,qCAAqC;AAAA,MAC/C,OAAO,KAAK;AAAA,MACZ,OAAO,oBAAoB,WAAW,WAAW,IAAI,SAAS,QAAQ;AAAA,IAC1E,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,sBAAsB,QAAQ;AAChC,WAAO,OAAO,0CAA0C;AAAA,MACpD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB;AAClB,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,oBAAoB,WAAW;AACjC,WAAO,OAAO,uCAAuC;AAAA,MACjD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,cAAc,OAAO;AACvB,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,cAAc,OAAO;AACvB,WAAO,OAAO,iCAAiC;AAAA,MAC3C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM,eAAe,MAAM;AACvB,WAAO,OAAO,kCAAkC;AAAA,MAC5C,OAAO,KAAK;AAAA,MACZ,OAAO,OAAO,eAAe,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,eAAe,OAAO;AACxB,WAAO,OAAO,kCAAkC;AAAA,MAC5C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,0BAA0B,SAAS;AACrC,WAAO,OAAO,+CAA+C;AAAA,MACzD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB,OAAO;AAC1B,WAAO,OAAO,qCAAqC;AAAA,MAC/C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SAAS,OAAO;AAClB,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,UAAU,SAAS;AACrB,WAAO,KAAK,OAAO,WAAW,gBAAgB,CAAC,MAAM;AACjD,QAAE,UAAU,IAAI,aAAa,EAAE,OAAO;AACtC,cAAQ,CAAC;AAAA,IACb,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,QAAQ,SAAS;AACnB,WAAO,KAAK,OAAO,WAAW,cAAc,CAAC,MAAM;AAC/C,QAAE,UAAU,IAAI,iBAAiB,EAAE,OAAO;AAC1C,cAAQ,CAAC;AAAA,IACb,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM,iBAAiB,SAAS;AAE5B,WAAO,KAAK,OAAO,WAAW,wBAAwB,OAAO,UAAU;AACnE,YAAM,MAAM,IAAI,oBAAoB,KAAK;AACzC,YAAM,QAAQ,GAAG;AACjB,UAAI,CAAC,IAAI,iBAAiB,GAAG;AACzB,cAAM,KAAK,QAAQ;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,eAAe,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AACrE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACxE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,eAAe,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACpE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,iBAAiB,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AACvE,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,QAAQ,EAAE,CAAC;AAAA,IACpD,CAAC;AACD,WAAO,MAAM;AACT,mBAAa;AACb,mBAAa;AACb,uBAAiB;AACjB,qBAAe;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,eAAe,SAAS;AAC1B,UAAM,gBAAgB,MAAM,KAAK,OAAO,WAAW,cAAc,CAAC,UAAU;AACxE,cAAQ,EAAE,GAAG,OAAO,SAAS,KAAK,CAAC;AAAA,IACvC,CAAC;AACD,UAAM,eAAe,MAAM,KAAK,OAAO,WAAW,aAAa,CAAC,UAAU;AACtE,cAAQ,EAAE,GAAG,OAAO,SAAS,MAAM,CAAC;AAAA,IACxC,CAAC;AACD,WAAO,MAAM;AACT,oBAAc;AACd,mBAAa;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,MAAM,eAAe,SAAS;AAC1B,WAAO,KAAK,OAAO,WAAW,6BAA6B,OAAO;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,eAAe,SAAS;AAC1B,WAAO,KAAK,OAAO,WAAW,sBAAsB,OAAO;AAAA,EAC/D;AACJ;AAMA,IAAI;AAAA,CACH,SAAUC,6BAA4B;AACnC,EAAAA,4BAA2B,UAAU,IAAI;AACzC,EAAAA,4BAA2B,UAAU,IAAI;AACzC,EAAAA,4BAA2B,SAAS,IAAI;AAC5C,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAMlE,IAAI;AAAA,CACH,SAAUC,SAAQ;AAMf,EAAAA,QAAO,iBAAiB,IAAI;AAM5B,EAAAA,QAAO,OAAO,IAAI;AAMlB,EAAAA,QAAO,MAAM,IAAI;AAMjB,EAAAA,QAAO,aAAa,IAAI;AAMxB,EAAAA,QAAO,WAAW,IAAI;AAItB,EAAAA,QAAO,UAAU,IAAI;AAIrB,EAAAA,QAAO,WAAW,IAAI;AAItB,EAAAA,QAAO,MAAM,IAAI;AAIjB,EAAAA,QAAO,SAAS,IAAI;AAIpB,EAAAA,QAAO,SAAS,IAAI;AAIpB,EAAAA,QAAO,YAAY,IAAI;AAIvB,EAAAA,QAAO,OAAO,IAAI;AAIlB,EAAAA,QAAO,kBAAkB,IAAI;AAI7B,EAAAA,QAAO,WAAW,IAAI;AAItB,EAAAA,QAAO,cAAc,IAAI;AAIzB,EAAAA,QAAO,SAAS,IAAI;AAIpB,EAAAA,QAAO,mBAAmB,IAAI;AAI9B,EAAAA,QAAO,uBAAuB,IAAI;AAIlC,EAAAA,QAAO,qBAAqB,IAAI;AAIhC,EAAAA,QAAO,MAAM,IAAI;AAQjB,EAAAA,QAAO,MAAM,IAAI;AAQjB,EAAAA,QAAO,SAAS,IAAI;AAIpB,EAAAA,QAAO,QAAQ,IAAI;AAInB,EAAAA,QAAO,YAAY,IAAI;AAIvB,EAAAA,QAAO,aAAa,IAAI;AAC5B,GAAG,WAAW,SAAS,CAAC,EAAE;AAQ1B,IAAI;AAAA,CACH,SAAUC,cAAa;AAIpB,EAAAA,aAAY,0BAA0B,IAAI;AAI1C,EAAAA,aAAY,QAAQ,IAAI;AAIxB,EAAAA,aAAY,UAAU,IAAI;AAC9B,GAAG,gBAAgB,cAAc,CAAC,EAAE;;;ACx0DpC,SAAS,oBAAoB;AACzB,SAAO,IAAI,QAAQ,iBAAiB,GAAG,OAAO,oBAAoB,SAAS,eAAe,OAAO;AAAA;AAAA,IAE7F,MAAM;AAAA,EACV,CAAC;AACL;AAMA,eAAe,iBAAiB;AAC5B,SAAO,OAAO,iCAAiC,EAAE,KAAK,CAAC,aAAa,SAAS,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,OAAO,EAAE,aAAa;AAAA;AAAA,IAE1H,MAAM;AAAA,EACV,CAAC,GAAG,EAAE,OAAO;AAAA;AAAA,IAET,MAAM;AAAA,EACV,CAAC,CAAC,CAAC;AACP;AAGA,IAAMC,oBAAmB,CAAC,mBAAmB,eAAe;AAyD5D,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCV,YAAYC,SAAQ,OAAO,SAAS;AAChC,SAAK,SAASA;AACd,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAEnC,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,aAAO,iCAAiC;AAAA,QACpC,aAAaA,QAAO;AAAA,QACpB,SAAS;AAAA,UACL,GAAG;AAAA,UACH;AAAA,QACJ;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,WAAW,OAAO;AAC3B,QAAI;AACJ,YAAQ,MAAM,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC3G;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,aAAa;AAChB,WAAO,kBAAkB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,WAAO,eAAe;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B,QAAQ,EAAE,MAAM,WAAW,OAAO,KAAK,MAAM;AAAA,IACjD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,SAAS;AAAA,MACxB,QAAQ,EAAE,MAAM,WAAW,OAAO,KAAK,MAAM;AAAA,IACjD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAID,kBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,OAAO,QAAQ,OAAO,SAAS;AACjC,QAAIA,kBAAiB,SAAS,KAAK,GAAG;AAElC,iBAAW,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,GAAG;AAC/C,gBAAQ;AAAA,UACJ;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,WAAO,OAAO,QAAQ,OAAO,OAAO;AAAA,EACxC;AAAA;AAAA,EAEA,kBAAkB,OAAO,SAAS;AAC9B,QAAIA,kBAAiB,SAAS,KAAK,GAAG;AAClC,UAAI,EAAE,SAAS,KAAK,YAAY;AAE5B,aAAK,UAAU,KAAK,IAAI,CAAC,OAAO;AAAA,MACpC,OACK;AAED,aAAK,UAAU,KAAK,EAAE,KAAK,OAAO;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,WAAW;AACb,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ;AACV,WAAO,OAAO,gCAAgC;AAAA,MAC1C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,QAAQ,MAAM;AAChB,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,MACZ,OAAO,gBAAgB,OAAO,OAAO,IAAI,KAAK,IAAI;AAAA,IACtD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,UAAU;AACxB,WAAO,OAAO,uCAAuC;AAAA,MACjD,OAAO,KAAK;AAAA,MACZ,OAAO,oBAAoB,WAAW,WAAW,IAAI,SAAS,QAAQ;AAAA,IAC1E,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW;AACb,WAAO,OAAO,oCAAoC;AAAA,MAC9C,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,YAAY;AAC5B,WAAO,OAAO,0CAA0C;AAAA,MACpD,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,OAAO;AACT,WAAO,OAAO,+BAA+B;AAAA,MACzC,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QAAQ,aAAa;AACvB,WAAO,OAAO,mCAAmC;AAAA,MAC7C,OAAO,KAAK;AAAA,MACZ,OAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SAASC,SAAQ;AACnB,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,KAAK;AAAA,MACZ,QAAQ,OAAOA,YAAW,WAAWA,UAASA,QAAO;AAAA,IACzD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,uBAAuB;AACzB,WAAO,OAAO,wCAAwC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,mBAAmB,OAAO;AAC5B,WAAO,OAAO,+CAA+C,EAAE,MAAM,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,oBAAoB,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AAC1E,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACxE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,mBAAmB,MAAM,KAAK,OAAO,WAAW,WAAW,CAAC,UAAU;AACxE,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,QAAQ;AAAA,UACrB,UAAU,IAAI,iBAAiB,MAAM,QAAQ,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,UAAM,oBAAoB,MAAM,KAAK,OAAO,WAAW,YAAY,CAAC,UAAU;AAC1E,cAAQ,EAAE,GAAG,OAAO,SAAS,EAAE,MAAM,QAAQ,EAAE,CAAC;AAAA,IACpD,CAAC;AACD,WAAO,MAAM;AACT,wBAAkB;AAClB,uBAAiB;AACjB,uBAAiB;AACjB,wBAAkB;AAAA,IACtB;AAAA,EACJ;AACJ;;;AC9jBA,SAAS,0BAA0B;AAC/B,QAAM,UAAU,kBAAkB;AAElC,SAAO,IAAI,cAAc,QAAQ,OAAO,EAAE,MAAM,KAAK,CAAC;AAC1D;AAMA,eAAe,uBAAuB;AAClC,SAAO,OAAO,+BAA+B,EAAE,KAAK,CAAC,YAAY,QAAQ,IAAI,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA;AAAA,IAErG,MAAM;AAAA,EACV,CAAC,CAAC,CAAC;AACP;AAEA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBhB,YAAY,OAAO,UAAU,CAAC,GAAG;AAC7B,QAAI;AACJ,SAAK,QAAQ;AAEb,SAAK,YAAY,uBAAO,OAAO,IAAI;AAEnC,QAAI,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnE,aAAO,wCAAwC;AAAA,QAC3C,SAAS;AAAA,UACL,GAAG;AAAA,UACH,QAAQ,OAAO,QAAQ,WAAW,WAC5B,QAAQ,UACP,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,UACpE;AAAA,QACJ;AAAA,MACJ,CAAC,EACI,KAAK,YAAY,KAAK,KAAK,iBAAiB,CAAC,EAC7C,MAAM,OAAO,MAAM,KAAK,KAAK,iBAAiB,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,WAAW,OAAO;AAC3B,QAAI;AACJ,UAAM,WAAW,MAAM,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AACtH,QAAI,SAAS;AAET,aAAO,IAAI,eAAc,QAAQ,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,IAC1D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,aAAa;AAChB,WAAO,wBAAwB;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS;AAClB,WAAO,qBAAqB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,OAAO,OAAO,SAAS;AACzB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,MAC1B,QAAQ,EAAE,MAAM,iBAAiB,OAAO,KAAK,MAAM;AAAA,IACvD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,KAAK,OAAO,SAAS;AACvB,QAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACxC,aAAO,MAAM;AAET,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,kBAAU,OAAO,UAAU,QAAQ,OAAO,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,KAAK,OAAO,SAAS;AAAA,MACxB,QAAQ,EAAE,MAAM,iBAAiB,OAAO,KAAK,MAAM;AAAA,IACvD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,mBAAmB,OAAO;AAC5B,WAAO,OAAO,sCAAsC,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM;AACtE,aAAO,OAAO,+CAA+C,EAAE,MAAM,CAAC;AAAA,IAC1E,CAAC;AAAA,EACL;AACJ;AAEA,YAAY,eAAe,CAAC,QAAQ,OAAO,CAAC;AAE5C,SAAS,YAAY,WAAW,iBAAiB;AAC7C,GAAC,MAAM,QAAQ,eAAe,IACxB,kBACA,CAAC,eAAe,GAAG,QAAQ,CAAC,kBAAkB;AAChD,WAAO,oBAAoB,cAAc,SAAS,EAAE,QAAQ,CAAC,SAAS;AAClE,UAAI;AACJ,UAAI,OAAO,UAAU,cAAc,YAC5B,UAAU,aACV,QAAQ,UAAU;AACrB;AACJ,aAAO;AAAA,QAAe,UAAU;AAAA,QAAW;AAAA;AAAA,SAE1C,KAAK,OAAO,yBAAyB,cAAc,WAAW,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,uBAAO,OAAO,IAAI;AAAA,MAAC;AAAA,IAC9H,CAAC;AAAA,EACL,CAAC;AACL;", "names": ["TauriEvent", "UserAttentionType", "ProgressBarStatus", "BackgroundThrottlingPolicy", "Effect", "EffectState", "localTauriEvents", "window"]}