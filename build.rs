fn main() {
    // 跳过图标生成
    std::env::set_var("TAURI_SKIP_EMBEDDED_RESOURCES", "true");

    // 创建一个最小的 ICO 文件
    let ico_path = std::path::Path::new("icons/icon.ico");
    if !ico_path.exists() {
        std::fs::create_dir_all("icons").ok();
        // 创建一个更完整的有效 ICO 文件 (16x16, 32位)
        let mut ico_data = vec![
            // ICO header
            0x00, 0x00, // Reserved
            0x01, 0x00, // Type (1 = ICO)
            0x01, 0x00, // Number of images

            // Image directory entry
            0x10,       // Width (16)
            0x10,       // Height (16)
            0x00,       // Color count (0 = >256 colors)
            0x00,       // Reserved
            0x01, 0x00, // Color planes
            0x20, 0x00, // Bits per pixel (32)
            0x00, 0x04, 0x00, 0x00, // Size of image data (1024 bytes)
            0x16, 0x00, 0x00, 0x00, // Offset to image data
        ];

        // BITMAPINFOHEADER
        ico_data.extend_from_slice(&[
            0x28, 0x00, 0x00, 0x00, // Size of header (40)
            0x10, 0x00, 0x00, 0x00, // Width (16)
            0x20, 0x00, 0x00, 0x00, // Height (32 = 16*2 for ICO)
            0x01, 0x00,             // Planes
            0x20, 0x00,             // Bits per pixel (32)
            0x00, 0x00, 0x00, 0x00, // Compression (0 = none)
            0x00, 0x04, 0x00, 0x00, // Image size (1024)
            0x00, 0x00, 0x00, 0x00, // X pixels per meter
            0x00, 0x00, 0x00, 0x00, // Y pixels per meter
            0x00, 0x00, 0x00, 0x00, // Colors used
            0x00, 0x00, 0x00, 0x00, // Important colors
        ]);

        // 16x16 RGBA pixel data (blue background)
        for _ in 0..256 {
            ico_data.extend_from_slice(&[0xFF, 0x00, 0x00, 0xFF]); // Blue BGRA
        }

        // AND mask (16x16 bits, all 0 = opaque)
        for _ in 0..32 {
            ico_data.push(0x00);
        }

        std::fs::write(ico_path, &ico_data).ok();
    }

    tauri_build::build()
}
