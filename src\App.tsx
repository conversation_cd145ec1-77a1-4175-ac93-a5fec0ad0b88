import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/tauri";
import TitleBar from "./components/TitleBar";
import Sidebar from "./components/Sidebar";
import MainContent from "./components/MainContent";
import { AppProvider } from "./context/AppContext";

function App() {
  const [currentView, setCurrentView] = useState("home");

  return (
    <AppProvider>
      <div className="flex flex-col h-screen bg-gray-900 text-white overflow-hidden">
        <TitleBar />
        <div className="flex flex-1">
          <div className="w-64 bg-gray-800 border-r border-gray-700" data-tauri-drag-region>
            <Sidebar currentView={currentView} onViewChange={setCurrentView} />
          </div>
          <MainContent currentView={currentView} />
        </div>
      </div>
    </AppProvider>
  );
}

export default App;
