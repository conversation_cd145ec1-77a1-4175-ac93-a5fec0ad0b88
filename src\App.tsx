import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/tauri";
import TitleBar from "./components/TitleBar";
import Sidebar from "./components/Sidebar";
import MainContent from "./components/MainContent";
import { AppProvider } from "./context/AppContext";

function App() {
  const [currentView, setCurrentView] = useState("home");

  return (
    <AppProvider>
      <div className="flex flex-col h-screen bg-gray-900 text-white overflow-hidden" data-tauri-drag-region>
        <TitleBar />
        <div className="flex flex-1">
          <Sidebar currentView={currentView} onViewChange={setCurrentView} />
          <MainContent currentView={currentView} />
        </div>
      </div>
    </AppProvider>
  );
}

export default App;
