# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-cursor-grab"
description = "Enables the set_cursor_grab command without any pre-configured scope."
commands.allow = ["set_cursor_grab"]

[[permission]]
identifier = "deny-set-cursor-grab"
description = "Denies the set_cursor_grab command without any pre-configured scope."
commands.deny = ["set_cursor_grab"]
