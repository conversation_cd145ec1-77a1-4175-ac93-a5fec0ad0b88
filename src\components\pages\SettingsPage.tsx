import React, { useState } from 'react';
import { Settings, Monitor, Volume2, Gamepad2, Shield, Folder, Bell, Palette } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    // 显示设置
    resolution: '1920x1080',
    fullscreen: true,
    vsync: true,
    frameRate: 60,
    
    // 音频设置
    masterVolume: 80,
    musicVolume: 70,
    effectVolume: 85,
    voiceVolume: 60,
    
    // 游戏设置
    autoSave: true,
    showDamageNumbers: true,
    showPlayerNames: true,
    enablePvP: false,
    
    // 启动器设置
    autoUpdate: true,
    startWithWindows: false,
    minimizeToTray: true,
    showNotifications: true,
    
    // 主题设置
    theme: 'dark',
    language: 'zh-CN',
    
    // 安全设置
    rememberPassword: false,
    twoFactorAuth: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    // 这里可以调用 Tauri 命令保存设置
    console.log('保存设置:', settings);
    alert('设置已保存！');
  };

  const resetSettings = () => {
    if (confirm('确定要重置所有设置吗？')) {
      // 重置为默认设置
      console.log('重置设置');
    }
  };

  return (
    <div className="p-8 space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center space-x-2">
          <Settings size={32} />
          <span>设置</span>
        </h1>
        <div className="space-x-3">
          <button
            onClick={resetSettings}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
            data-tauri-drag-region="false"
          >
            重置设置
          </button>
          <button
            onClick={saveSettings}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            data-tauri-drag-region="false"
          >
            保存设置
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 显示设置 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Monitor size={24} />
            <span>显示设置</span>
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">分辨率</label>
              <select
                value={settings.resolution}
                onChange={(e) => handleSettingChange('resolution', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="1920x1080">1920x1080</option>
                <option value="1680x1050">1680x1050</option>
                <option value="1440x900">1440x900</option>
                <option value="1366x768">1366x768</option>
                <option value="1280x720">1280x720</option>
              </select>
            </div>

            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.fullscreen}
                  onChange={(e) => handleSettingChange('fullscreen', e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span>全屏模式</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.vsync}
                  onChange={(e) => handleSettingChange('vsync', e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span>垂直同步</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                帧率限制: {settings.frameRate} FPS
              </label>
              <input
                type="range"
                min="30"
                max="144"
                step="30"
                value={settings.frameRate}
                onChange={(e) => handleSettingChange('frameRate', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>30</span>
                <span>60</span>
                <span>90</span>
                <span>120</span>
                <span>144</span>
              </div>
            </div>
          </div>
        </div>

        {/* 音频设置 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Volume2 size={24} />
            <span>音频设置</span>
          </h2>
          
          <div className="space-y-4">
            {[
              { key: 'masterVolume', label: '主音量' },
              { key: 'musicVolume', label: '背景音乐' },
              { key: 'effectVolume', label: '音效' },
              { key: 'voiceVolume', label: '语音' }
            ].map(({ key, label }) => (
              <div key={key}>
                <label className="block text-sm font-medium mb-2">
                  {label}: {settings[key as keyof typeof settings]}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings[key as keyof typeof settings] as number}
                  onChange={(e) => handleSettingChange(key, parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 游戏设置 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Gamepad2 size={24} />
            <span>游戏设置</span>
          </h2>
          
          <div className="space-y-3">
            {[
              { key: 'autoSave', label: '自动保存' },
              { key: 'showDamageNumbers', label: '显示伤害数字' },
              { key: 'showPlayerNames', label: '显示玩家姓名' },
              { key: 'enablePvP', label: '启用PvP模式' }
            ].map(({ key, label }) => (
              <label key={key} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings[key as keyof typeof settings] as boolean}
                  onChange={(e) => handleSettingChange(key, e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span>{label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* 启动器设置 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Folder size={24} />
            <span>启动器设置</span>
          </h2>
          
          <div className="space-y-3">
            {[
              { key: 'autoUpdate', label: '自动更新' },
              { key: 'startWithWindows', label: '开机自启动' },
              { key: 'minimizeToTray', label: '最小化到系统托盘' },
              { key: 'showNotifications', label: '显示通知' }
            ].map(({ key, label }) => (
              <label key={key} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings[key as keyof typeof settings] as boolean}
                  onChange={(e) => handleSettingChange(key, e.target.checked)}
                  className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                />
                <span>{label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* 主题和语言 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Palette size={24} />
            <span>外观设置</span>
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">主题</label>
              <select
                value={settings.theme}
                onChange={(e) => handleSettingChange('theme', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="dark">深色主题</option>
                <option value="light">浅色主题</option>
                <option value="auto">跟随系统</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">语言</label>
              <select
                value={settings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
              >
                <option value="zh-CN">简体中文</option>
                <option value="zh-TW">繁体中文</option>
                <option value="en-US">English</option>
                <option value="ja-JP">日本語</option>
                <option value="ko-KR">한국어</option>
              </select>
            </div>
          </div>
        </div>

        {/* 安全设置 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4 flex items-center space-x-2">
            <Shield size={24} />
            <span>安全设置</span>
          </h2>
          
          <div className="space-y-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.rememberPassword}
                onChange={(e) => handleSettingChange('rememberPassword', e.target.checked)}
                className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
              />
              <span>记住密码</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.twoFactorAuth}
                onChange={(e) => handleSettingChange('twoFactorAuth', e.target.checked)}
                className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
              />
              <span>启用双重验证</span>
            </label>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-700">
            <button
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
              data-tauri-drag-region="false"
            >
              更改密码
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
