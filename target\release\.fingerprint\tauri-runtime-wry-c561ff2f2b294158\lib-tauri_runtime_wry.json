{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 6620145502275485423, "deps": [[376837177317575824, "softbuffer", false, 11302137362929675364], [2013030631243296465, "webview2_com", false, 8039702730747510435], [2671782512663819132, "tauri_utils", false, 15460487741040345240], [3150220818285335163, "url", false, 14565937691566033650], [3722963349756955755, "once_cell", false, 10802249507119394238], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [5986029879202738730, "log", false, 7783758905256315459], [6089812615193535349, "tauri_runtime", false, 17817977468728473616], [8826339825490770380, "tao", false, 15182620766971240201], [9010263965687315507, "http", false, 1862789691165185595], [9141053277961803901, "wry", false, 12445296021393844738], [11599800339996261026, "build_script_build", false, 2261786933350007644], [14585479307175734061, "windows", false, 3114519431610511065]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-c561ff2f2b294158\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}