import React from 'react';
import HomePage from './pages/HomePage';
import LaunchPage from './pages/LaunchPage';
import NewsPage from './pages/NewsPage';
import UpdatesPage from './pages/UpdatesPage';
import EventsPage from './pages/EventsPage';
import SettingsPage from './pages/SettingsPage';
import DownloadPage from './pages/DownloadPage';

interface MainContentProps {
  currentView: string;
}

const MainContent: React.FC<MainContentProps> = ({ currentView }) => {
  const renderContent = () => {
    switch (currentView) {
      case 'home':
        return <HomePage />;
      case 'launch':
        return <LaunchPage />;
      case 'news':
        return <NewsPage />;
      case 'updates':
        return <UpdatesPage />;
      case 'events':
        return <EventsPage />;
      case 'settings':
        return <SettingsPage />;
      case 'download':
        return <DownloadPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div
        className="flex-1 overflow-y-auto"
        data-scrollable="true"
        data-tauri-drag-region="false"
      >
        {renderContent()}
      </div>
    </div>
  );
};

export default MainContent;
