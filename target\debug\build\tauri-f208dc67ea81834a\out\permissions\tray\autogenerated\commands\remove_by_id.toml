# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-remove-by-id"
description = "Enables the remove_by_id command without any pre-configured scope."
commands.allow = ["remove_by_id"]

[[permission]]
identifier = "deny-remove-by-id"
description = "Denies the remove_by_id command without any pre-configured scope."
commands.deny = ["remove_by_id"]
