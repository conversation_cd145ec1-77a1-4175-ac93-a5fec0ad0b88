{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 1554061257607164671, "path": 1313671789237867526, "deps": [[2013030631243296465, "webview2_com", false, 8039702730747510435], [3334271191048661305, "windows_version", false, 11384153312864473936], [3722963349756955755, "once_cell", false, 10802249507119394238], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [5628259161083531273, "windows_core", false, 6476949254436996837], [7606335748176206944, "dpi", false, 12077686462733247621], [9010263965687315507, "http", false, 1862789691165185595], [9141053277961803901, "build_script_build", false, 6535936410858522701], [10806645703491011684, "thiserror", false, 13847597657789702417], [11989259058781683633, "dunce", false, 486707178400923159], [14585479307175734061, "windows", false, 3114519431610511065], [16727543399706004146, "cookie", false, 1402681375577970861]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-67c69e68ea26ced0\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}