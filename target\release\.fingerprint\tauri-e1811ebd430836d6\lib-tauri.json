{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 3919467103513695935, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [1200537532907108615, "url<PERSON><PERSON>n", false, 1908635901779572286], [2013030631243296465, "webview2_com", false, 8039702730747510435], [2671782512663819132, "tauri_utils", false, 15460487741040345240], [3150220818285335163, "url", false, 14565937691566033650], [3331586631144870129, "getrandom", false, 17283948573081985853], [4143744114649553716, "raw_window_handle", false, 11946703729155000207], [4494683389616423722, "muda", false, 7763170468438597247], [4919829919303820331, "serialize_to_javascript", false, 4281474058925537243], [5986029879202738730, "log", false, 7783758905256315459], [6089812615193535349, "tauri_runtime", false, 17817977468728473616], [7573826311589115053, "tauri_macros", false, 11581202150063613603], [9010263965687315507, "http", false, 1862789691165185595], [9538054652646069845, "tokio", false, 5970496291290527155], [9689903380558560274, "serde", false, 9841288889961963921], [10229185211513642314, "mime", false, 9651018063964816897], [10806645703491011684, "thiserror", false, 13847597657789702417], [11599800339996261026, "tauri_runtime_wry", false, 10864598713949363160], [11989259058781683633, "dunce", false, 486707178400923159], [12565293087094287914, "window_vibrancy", false, 10612105401006876559], [12986574360607194341, "serde_repr", false, 15487894934108931834], [13077543566650298139, "heck", false, 11675331362666342109], [13625485746686963219, "anyhow", false, 2431689092402825966], [14039947826026167952, "build_script_build", false, 4390194270912513681], [14585479307175734061, "windows", false, 3114519431610511065], [15367738274754116744, "serde_json", false, 13225267877164477003], [16928111194414003569, "dirs", false, 10435552808141219587], [17155886227862585100, "glob", false, 4801171762843958688]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-e1811ebd430836d6\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}