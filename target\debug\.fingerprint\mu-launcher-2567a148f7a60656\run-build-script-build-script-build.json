{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7767367476540183319, "build_script_build", false, 2905793968951489053], [14039947826026167952, "build_script_build", false, 11009446261171214479]], "local": [{"RerunIfChanged": {"output": "debug\\build\\mu-launcher-2567a148f7a60656\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}