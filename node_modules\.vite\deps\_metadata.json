{"hash": "2896ba99", "browserHash": "4d21c881", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b75e86ea", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2840e1cc", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9ba06dc3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "db290faa", "needsInterop": true}, "@tauri-apps/api/core": {"src": "../../@tauri-apps/api/core.js", "file": "@tauri-apps_api_core.js", "fileHash": "3ac9f2af", "needsInterop": false}, "@tauri-apps/api/webviewWindow": {"src": "../../@tauri-apps/api/webviewWindow.js", "file": "@tauri-apps_api_webviewWindow.js", "fileHash": "c89af8c2", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "980b7f30", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "85680c4a", "needsInterop": true}}, "chunks": {"chunk-UJENAZSL": {"file": "chunk-UJENAZSL.js"}, "chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}