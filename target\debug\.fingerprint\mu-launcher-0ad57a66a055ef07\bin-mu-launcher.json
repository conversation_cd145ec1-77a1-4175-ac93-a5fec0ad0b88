{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 5003308703183214392, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[7767367476540183319, "mu_launcher", false, 14645935821941176086], [7767367476540183319, "build_script_build", false, 6254757677891354806], [8218178811151724123, "reqwest", false, 2255722606144338595], [9538054652646069845, "tokio", false, 6351764626681835630], [9689903380558560274, "serde", false, 14326913226427773042], [13625485746686963219, "anyhow", false, 16521415306001099308], [14039947826026167952, "tauri", false, 4598931963810347571], [15367738274754116744, "serde_json", false, 6815494230538151593]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mu-launcher-0ad57a66a055ef07\\dep-bin-mu-launcher", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}