# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-cursor-position"
description = "Enables the cursor_position command without any pre-configured scope."
commands.allow = ["cursor_position"]

[[permission]]
identifier = "deny-cursor-position"
description = "Denies the cursor_position command without any pre-configured scope."
commands.deny = ["cursor_position"]
