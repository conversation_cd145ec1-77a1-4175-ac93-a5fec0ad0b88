import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Calendar, Clock, Tag } from 'lucide-react';

interface NewsItem {
  id: number;
  title: string;
  content: string;
  date: string;
  category?: string;
}

const NewsPage: React.FC = () => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    loadNews();
  }, []);

  const loadNews = async () => {
    try {
      setLoading(true);
      const newsData = await invoke('get_news') as NewsItem[];
      // 添加一些示例数据
      const extendedNews = [
        ...newsData,
        {
          id: 3,
          title: "新职业\"魔剑士\"即将上线",
          content: "全新职业魔剑士将在下个版本中与大家见面，拥有独特的技能体系和装备系统。",
          date: "2024-01-13",
          category: "更新"
        },
        {
          id: 4,
          title: "春节活动预告",
          content: "春节期间将举办丰富多彩的游戏活动，包括限时副本、特殊奖励等。",
          date: "2024-01-12",
          category: "活动"
        },
        {
          id: 5,
          title: "服务器优化完成",
          content: "经过技术团队的努力，服务器性能得到显著提升，游戏体验更加流畅。",
          date: "2024-01-11",
          category: "公告"
        },
        {
          id: 6,
          title: "新地图\"幻境森林\"开放",
          content: "全新地图幻境森林现已开放，拥有独特的生态系统和丰富的探索内容。玩家可以在这里发现稀有材料和隐藏任务。",
          date: "2024-01-10",
          category: "更新"
        },
        {
          id: 7,
          title: "公会系统升级",
          content: "公会系统迎来重大升级，新增公会战、公会商店、公会技能等功能，让公会玩法更加丰富多样。",
          date: "2024-01-09",
          category: "更新"
        },
        {
          id: 8,
          title: "限时活动\"冬日庆典\"",
          content: "冬日庆典活动正在进行中，参与活动可获得限定装备、稀有道具和丰厚奖励。活动截止到本月底。",
          date: "2024-01-08",
          category: "活动"
        },
        {
          id: 9,
          title: "游戏平衡性调整",
          content: "本次更新对多个职业进行了平衡性调整，优化了技能效果和数值，提升整体游戏体验。",
          date: "2024-01-07",
          category: "更新"
        },
        {
          id: 10,
          title: "新手指导系统优化",
          content: "为了帮助新玩家更好地上手游戏，我们优化了新手指导系统，增加了更详细的教程和提示。",
          date: "2024-01-06",
          category: "公告"
        }
      ];
      setNews(extendedNews);
    } catch (error) {
      console.error('获取资讯失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const categories = ['all', '更新', '活动', '公告'];
  const filteredNews = selectedCategory === 'all' 
    ? news 
    : news.filter(item => item.category === selectedCategory);

  if (loading) {
    return (
      <div className="p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">最新资讯</h1>
        <div className="flex space-x-2">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              data-tauri-drag-region="false"
            >
              {category === 'all' ? '全部' : category}
            </button>
          ))}
        </div>
      </div>

      <div className="grid gap-6">
        {filteredNews.map((item) => (
          <div key={item.id} className="bg-gray-800 rounded-xl p-6 hover:bg-gray-750 transition-colors">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h2 className="text-xl font-bold mb-2">{item.title}</h2>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Calendar size={16} />
                    <span>{item.date}</span>
                  </div>
                  {item.category && (
                    <div className="flex items-center space-x-1">
                      <Tag size={16} />
                      <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                        {item.category}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">{item.content}</p>
            <div className="mt-4 pt-4 border-t border-gray-700">
              <button
                className="text-blue-400 hover:text-blue-300 font-medium transition-colors"
                data-tauri-drag-region="false"
              >
                阅读更多 →
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredNews.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400 text-lg">暂无相关资讯</p>
        </div>
      )}
    </div>
  );
};

export default NewsPage;
