import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Download, CheckCircle, AlertCircle, RefreshCw, Pause, Play } from 'lucide-react';

interface UpdateInfo {
  version: string;
  size: string;
  description: string;
  releaseDate: string;
  isRequired: boolean;
}

const DownloadPage: React.FC = () => {
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadSpeed, setDownloadSpeed] = useState('0 KB/s');
  const [hasUpdate, setHasUpdate] = useState(false);

  useEffect(() => {
    checkForUpdates();
  }, []);

  const checkForUpdates = async () => {
    setIsChecking(true);
    try {
      // 模拟检查更新
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟有更新可用
      const mockUpdate: UpdateInfo = {
        version: "v2.1.1",
        size: "156.7 MB",
        description: "修复了一些已知问题，优化了游戏性能。",
        releaseDate: "2024-01-16",
        isRequired: false
      };
      
      setUpdateInfo(mockUpdate);
      setHasUpdate(true);
    } catch (error) {
      console.error('检查更新失败:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const startDownload = () => {
    setIsDownloading(true);
    setIsPaused(false);
    
    // 模拟下载进度
    const interval = setInterval(() => {
      setDownloadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsDownloading(false);
          setHasUpdate(false);
          return 100;
        }
        return prev + Math.random() * 3;
      });
      
      // 模拟下载速度变化
      const speeds = ['1.2 MB/s', '2.5 MB/s', '3.1 MB/s', '1.8 MB/s', '2.9 MB/s'];
      setDownloadSpeed(speeds[Math.floor(Math.random() * speeds.length)]);
    }, 200);
  };

  const pauseDownload = () => {
    setIsPaused(true);
    setIsDownloading(false);
  };

  const resumeDownload = () => {
    setIsPaused(false);
    setIsDownloading(true);
  };

  const cancelDownload = () => {
    setIsDownloading(false);
    setIsPaused(false);
    setDownloadProgress(0);
    setDownloadSpeed('0 KB/s');
  };

  return (
    <div className="p-8 space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center space-x-2">
          <Download size={32} />
          <span>更新下载</span>
        </h1>
        <button
          onClick={checkForUpdates}
          disabled={isChecking}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg font-medium transition-colors"
          data-tauri-drag-region="false"
        >
          <RefreshCw size={16} className={isChecking ? 'animate-spin' : ''} />
          <span>{isChecking ? '检查中...' : '检查更新'}</span>
        </button>
      </div>

      {/* 当前版本信息 */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-xl font-bold mb-4">当前版本信息</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <p className="text-2xl font-bold text-blue-400">v2.1.0</p>
            <p className="text-sm text-gray-400">当前版本</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <p className="text-2xl font-bold text-green-400">最新</p>
            <p className="text-sm text-gray-400">更新状态</p>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <p className="text-2xl font-bold text-purple-400">2024-01-15</p>
            <p className="text-sm text-gray-400">安装日期</p>
          </div>
        </div>
      </div>

      {/* 更新检查结果 */}
      {isChecking && (
        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-center space-x-3">
            <RefreshCw size={24} className="animate-spin text-blue-400" />
            <span className="text-lg">正在检查更新...</span>
          </div>
        </div>
      )}

      {/* 有更新可用 */}
      {hasUpdate && updateInfo && !isChecking && (
        <div className="bg-gradient-to-r from-blue-900 to-blue-800 rounded-xl p-6 border border-blue-500">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-blue-300 mb-2">发现新版本！</h2>
              <div className="space-y-2">
                <p><strong>版本:</strong> {updateInfo.version}</p>
                <p><strong>大小:</strong> {updateInfo.size}</p>
                <p><strong>发布日期:</strong> {updateInfo.releaseDate}</p>
                <p><strong>类型:</strong> {updateInfo.isRequired ? '必需更新' : '可选更新'}</p>
              </div>
            </div>
            {updateInfo.isRequired && (
              <div className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                必需更新
              </div>
            )}
          </div>
          
          <div className="mb-4">
            <h3 className="font-semibold mb-2">更新内容:</h3>
            <p className="text-gray-300">{updateInfo.description}</p>
          </div>

          {!isDownloading && !isPaused && downloadProgress === 0 && (
            <button
              onClick={startDownload}
              className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-bold flex items-center space-x-2 transition-colors"
              data-tauri-drag-region="false"
            >
              <Download size={20} />
              <span>开始下载</span>
            </button>
          )}
        </div>
      )}

      {/* 下载进度 */}
      {(isDownloading || isPaused || downloadProgress > 0) && (
        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">下载进度</h2>
            <div className="flex items-center space-x-2">
              {isDownloading && (
                <button
                  onClick={pauseDownload}
                  className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded font-medium flex items-center space-x-1"
                  data-tauri-drag-region="false"
                >
                  <Pause size={16} />
                  <span>暂停</span>
                </button>
              )}
              {isPaused && (
                <button
                  onClick={resumeDownload}
                  className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded font-medium flex items-center space-x-1"
                  data-tauri-drag-region="false"
                >
                  <Play size={16} />
                  <span>继续</span>
                </button>
              )}
              <button
                onClick={cancelDownload}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded font-medium"
                data-tauri-drag-region="false"
              >
                取消
              </button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>进度: {downloadProgress.toFixed(1)}%</span>
              <span>速度: {downloadSpeed}</span>
            </div>
            
            <div className="w-full bg-gray-700 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${downloadProgress}%` }}
              ></div>
            </div>

            {downloadProgress === 100 && (
              <div className="flex items-center space-x-2 text-green-400">
                <CheckCircle size={20} />
                <span className="font-semibold">下载完成！正在安装...</span>
              </div>
            )}

            {isPaused && (
              <div className="flex items-center space-x-2 text-yellow-400">
                <AlertCircle size={20} />
                <span>下载已暂停</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 无更新可用 */}
      {!hasUpdate && !isChecking && (
        <div className="bg-gray-800 rounded-xl p-6 text-center">
          <CheckCircle size={48} className="text-green-400 mx-auto mb-4" />
          <h2 className="text-xl font-bold mb-2">已是最新版本</h2>
          <p className="text-gray-400">您的游戏已经是最新版本，无需更新。</p>
        </div>
      )}

      {/* 更新历史 */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-xl font-bold mb-4">更新历史</h2>
        <div className="space-y-3">
          {[
            { version: 'v2.1.0', date: '2024-01-15', status: '已安装' },
            { version: 'v2.0.5', date: '2024-01-10', status: '已安装' },
            { version: 'v2.0.4', date: '2024-01-05', status: '已安装' },
          ].map((update, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <CheckCircle size={20} className="text-green-400" />
                <div>
                  <p className="font-semibold">{update.version}</p>
                  <p className="text-sm text-gray-400">{update.date}</p>
                </div>
              </div>
              <span className="text-sm text-green-400">{update.status}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DownloadPage;
