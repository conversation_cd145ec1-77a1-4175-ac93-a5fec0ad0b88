# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-name"
description = "Enables the name command without any pre-configured scope."
commands.allow = ["name"]

[[permission]]
identifier = "deny-name"
description = "Denies the name command without any pre-configured scope."
commands.deny = ["name"]
