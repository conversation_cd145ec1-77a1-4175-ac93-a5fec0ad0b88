import React from 'react';
import { 
  Home, 
  Play, 
  Newspaper, 
  FileText, 
  Calendar, 
  Settings, 
  Download 
} from 'lucide-react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, onViewChange }) => {
  const menuItems = [
    { id: 'home', label: '首页', icon: Home },
    { id: 'launch', label: '启动游戏', icon: Play },
    { id: 'news', label: '最新资讯', icon: Newspaper },
    { id: 'updates', label: '更新日志', icon: FileText },
    { id: 'events', label: '活动信息', icon: Calendar },
    { id: 'settings', label: '设置', icon: Settings },
    { id: 'download', label: '下载更新', icon: Download },
  ];

  return (
    <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
      {/* Logo区域 */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">E</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">E-MU.ONLINE</h1>
            <p className="text-sm text-gray-400">Game Launcher</p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onViewChange(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                  data-tauri-drag-region="false"
                >
                  <Icon size={20} />
                  <span className="font-medium">{item.label}</span>
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* 底部信息 */}
      <div className="p-4 border-t border-gray-700">
        <div className="text-center">
          <p className="text-xs text-gray-500">版本 v1.0.0</p>
          <p className="text-xs text-gray-500 mt-1">© 2024 E-MU.ONLINE</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
