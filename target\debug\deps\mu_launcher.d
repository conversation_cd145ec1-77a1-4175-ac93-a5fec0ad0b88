C:\Users\<USER>\Documents\augment-projects\MuLauncher\target\debug\deps\mu_launcher.d: src\main.rs C:\Users\<USER>\Documents\augment-projects\MuLauncher\target\debug\build\mu-launcher-2567a148f7a60656\out/864de85e032deadb7d5291a726c7db6b3da6a75b5b10c5463427bad513e94fbd

C:\Users\<USER>\Documents\augment-projects\MuLauncher\target\debug\deps\mu_launcher.exe: src\main.rs C:\Users\<USER>\Documents\augment-projects\MuLauncher\target\debug\build\mu-launcher-2567a148f7a60656\out/864de85e032deadb7d5291a726c7db6b3da6a75b5b10c5463427bad513e94fbd

src\main.rs:
C:\Users\<USER>\Documents\augment-projects\MuLauncher\target\debug\build\mu-launcher-2567a148f7a60656\out/864de85e032deadb7d5291a726c7db6b3da6a75b5b10c5463427bad513e94fbd:

# <AUTHOR> <EMAIL>
# env-dep:CARGO_PKG_DESCRIPTION=E-MU.ONLINE Game Launcher
# env-dep:CARGO_PKG_NAME=mu-launcher
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Documents\\augment-projects\\MuLauncher\\target\\debug\\build\\mu-launcher-2567a148f7a60656\\out
